# 🔥 دليل إعداد Firebase للبوت

## 📋 المحتويات
1. [إعداد مشروع Firebase](#إعداد-مشروع-firebase)
2. [تكوين Firestore Database](#تكوين-firestore-database)
3. [بنية قاعدة البيانات](#بنية-قاعدة-البيانات)
4. [الأمان والقواعد](#الأمان-والقواعد)
5. [التكامل مع البوت](#التكامل-مع-البوت)

## 🚀 إعداد مشروع Firebase

### 1️⃣ إنشاء مشروع جديد
1. افتح [Firebase Console](https://console.firebase.google.com)
2. انقر على "إضافة مشروع"
3. أدخل اسم المشروع (مثال: `telegram-trading-bot`)
4. اختر ما إذا كنت تريد تفعيل Google Analytics
5. انقر على "إنشاء المشروع"

### 2️⃣ الحصول على مفاتيح المشروع
1. انتقل إلى ⚙️ إعدادات المشروع
2. اختر "حسابات الخدمة"
3. انقر على "إنشاء مفتاح خدمة جديد"
4. قم بتحميل ملف JSON
5. احفظ الملف في مجلد المشروع باسم `serviceAccountKey.json`

## 💾 تكوين Firestore Database

### 1️⃣ إنشاء قاعدة البيانات
1. من القائمة الجانبية، اختر "Firestore Database"
2. انقر على "إنشاء قاعدة بيانات"
3. اختر وضع الإنتاج
4. حدد المنطقة الأقرب لمستخدميك

### 2️⃣ تثبيت مكتبات Firebase
```bash
pip install firebase-admin google-cloud-firestore
```

## 📊 بنية قاعدة البيانات

### 📚 Collections الرئيسية

#### 👥 users
```javascript
{
    userId: string,          // معرف المستخدم في تيليجرام
    username: string,        // اسم المستخدم
    chatId: number,         // معرف المحادثة
    subscriptionStatus: string,  // 'free' | 'premium'
    subscriptionExpiry: timestamp,
    language: string,       // 'ar' | 'en'
    favorites: array,       // العملات المفضلة
    createdAt: timestamp
}
```

#### 🔔 subscriptions
```javascript
{
    status: string,         // 'subscribed'
    subscription_date: string,  // تاريخ بداية الاشتراك
    expiry: string,        // تاريخ انتهاء الاشتراك
    transaction_id: string, // معرف المعاملة
    lang: string,          // 'ar' | 'en'
    features: array        // الميزات المتاحة
}
```

#### ⏰ alerts
```javascript
{
    alertId: string,        // معرف تلقائي
    userId: string,         // معرف المستخدم
    symbol: string,         // رمز العملة
    price: number,         // السعر المستهدف
    condition: string,      // 'above' | 'below'
    status: string,        // 'active' | 'triggered'
    createdAt: timestamp
}
```

#### 💰 transactions
```javascript
{
    userId: string,        // معرف المستخدم
    amount: number,       // قيمة المعاملة
    status: string,       // 'pending' | 'completed' | 'failed'
    createdAt: timestamp  // تاريخ إنشاء المعاملة
}
```

#### 🔑 backup_keys
```javascript
{
    key: string,          // مفتاح التشفير
    created_at: timestamp, // تاريخ الإنشاء
    previous_key: string  // المفتاح السابق (اختياري)
}
```

#### 💾 backups
```javascript
{
    encrypted_data: string,  // البيانات المشفرة
    metadata: {
        timestamp: string,
        checksum: string,
        salt: string,
        key_id: string
    }
}
```

#### ⚙️ user_settings
```javascript
{
    userId: string,       // معرف المستخدم
    language: string,     // تفضيلات اللغة
    currencies: array     // العملات المتابعة
}
```

#### 📊 daily_usage
```javascript
{
    date: string,         // تاريخ اليوم
    analyses: number,     // عدد التحليلات المستخدمة
    alerts: number       // عدد التنبيهات المستخدمة
}
```

#### 🆓 free_usage
```javascript
{
    date: string,         // تاريخ اليوم
    analyses: number,     // عدد التحليلات المجانية المتبقية (الافتراضي: 3)
    alerts: number       // عدد التنبيهات المجانية المتبقية (الافتراضي: 1)
}
```

#### 📈 stats
```javascript
{
    total_users: number,      // إجمالي عدد المستخدمين
    active_subscribers: number, // عدد المشتركين النشطين
    total_analyses: number,    // إجمالي التحليلات
    total_alerts: number,      // إجمالي التنبيهات
    last_update: timestamp    // آخر تحديث
}
```

#### 📋 periodic_reports
```javascript
{
    type: string,         // نوع التقرير
    data: object,         // بيانات التقرير
    createdAt: timestamp  // تاريخ الإنشاء
}
```

#### ⚙️ config
```javascript
{
    bot_token: string,    // توكن البوت
    owner_id: string,     // معرف المالك
    backup_interval: number, // فترة النسخ الاحتياطي
    stats_interval: number, // فترة تحديث الإحصائيات
    payment_methods: {     // طرق الدفع المتاحة
        crypto: {
            wallet_address: string,
            network: string,
            currency: string
        }
    },
    PAYMENT_AMOUNT: number // قيمة الاشتراك
}
```

#### 🔧 _system
```javascript
{
    _metadata: {
        version: string,
        last_update: timestamp,
        initialized: boolean,
        collections: array
    },
    _schema: object      // مخطط قاعدة البيانات
}
```

### ملاحظات هامة حول قواعد البيانات 📝

1. جميع الجداول تستخدم نظام التوثيق والصلاحيات لحماية البيانات
2. يتم تخزين التواريخ باستخدام Timestamp لضمان التوافق بين المناطق الزمنية
3. يتم استخدام المعرفات الفريدة (UUID) للوثائق حيثما أمكن
4. يتم تنفيذ النسخ الاحتياطي التلقائي للبيانات بشكل دوري
5. يتم استخدام الذاكرة المؤقتة (Redis) لتحسين الأداء وتقليل الطلبات على Firestore

## 🔒 الأمان والقواعد

### قواعد Firestore المقترحة
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // دالة مساعدة للتحقق من المستخدم المصرح له
    function isAuthorizedUser(userId) {
      return request.auth != null && request.auth.uid == userId;
    }
    
    // دالة مساعدة للتحقق من الاشتراك المميز
    function isPremiumUser(userId) {
      return get(/databases/$(database)/documents/users/$(userId)).data.subscriptionStatus == 'premium';
    }

    // قواعد المستخدمين
    match /users/{userId} {
      allow read: if isAuthorizedUser(userId);
      allow write: if isAuthorizedUser(userId);
    }
    
    // قواعد التنبيهات
    match /alerts/{alertId} {
      allow read: if request.auth != null && isPremiumUser(request.auth.uid);
      allow write: if request.auth != null;
    }

    // قواعد المعاملات
    match /transactions/{transactionId} {
      allow read: if request.auth != null && 
        resource.data.userId == request.auth.uid;
      allow create: if request.auth != null;
      allow update, delete: if false; // لا يسمح بالتعديل أو الحذف
    }

    // قواعد إعدادات المستخدم
    match /user_settings/{userId} {
      allow read, write: if isAuthorizedUser(userId);
    }

    // قواعد الاستخدام اليومي
    match /daily_usage/{userId} {
      allow read: if isAuthorizedUser(userId);
      allow write: if false; // يتم التحديث من خلال Cloud Functions فقط
    }

    // قواعد الاستخدام المجاني
    match /free_usage/{userId} {
      allow read: if isAuthorizedUser(userId);
      allow write: if false; // يتم التحديث من خلال Cloud Functions فقط
    }

    // قواعد النسخ الاحتياطي
    match /backup_keys/{keyId} {
      allow read, write: if false; // للمسؤولين فقط
    }

    // قواعد الإعدادات العامة
    match /config/{configId} {
      allow read: if true; // يمكن للجميع القراءة
      allow write: if false; // للمسؤولين فقط
    }
  }
}
```

## 🔄 التكامل مع البوت

### 1️⃣ تهيئة Firebase
```python
import firebase_admin
from firebase_admin import credentials, firestore

# تهيئة Firebase
cred = credentials.Certificate('serviceAccountKey.json')
firebase_admin.initialize_app(cred)
db = firestore.client()
```

### 2️⃣ أمثلة على العمليات الأساسية

#### إضافة مستخدم جديد
```python
def add_user(user_data):
    user_ref = db.collection('users').document(str(user_data['userId']))
    user_ref.set({
        'username': user_data['username'],
        'chatId': user_data['chatId'],
        'subscriptionStatus': 'free',
        'language': 'ar',
        'favorites': [],
        'createdAt': firestore.SERVER_TIMESTAMP
    })
```

#### إضافة تنبيه سعر
```python
def create_alert(alert_data):
    alert_ref = db.collection('alerts').document()
    alert_ref.set({
        'userId': alert_data['userId'],
        'symbol': alert_data['symbol'],
        'price': alert_data['price'],
        'condition': alert_data['condition'],
        'status': 'active',
        'createdAt': firestore.SERVER_TIMESTAMP
    })
```

#### تسجيل معاملة جديدة
```python
def record_transaction(user_id: str, amount: float):
    transaction_ref = db.collection('transactions').document()
    transaction_ref.set({
        'userId': user_id,
        'amount': amount,
        'status': 'pending',
        'createdAt': firestore.SERVER_TIMESTAMP
    })
```

#### تحديث إعدادات المستخدم
```python
def update_user_settings(user_id: str, settings: dict):
    settings_ref = db.collection('user_settings').document(user_id)
    settings_ref.set(settings, merge=True)
```

#### تحديث الاستخدام اليومي
```python
def update_daily_usage(user_id: str, usage_type: str):
    today = datetime.now().date().isoformat()
    usage_ref = db.collection('daily_usage').document(user_id)
    
    usage_ref.set({
        'date': today,
        usage_type: firestore.Increment(1)
    }, merge=True)
```

#### التحقق من الاستخدام المجاني
```python
def check_free_usage(user_id: str) -> dict:
    usage_ref = db.collection('free_usage').document(user_id)
    usage = usage_ref.get()
    
    if not usage.exists:
        return {
            'analyses': 3,  # عدد التحليلات المجانية الافتراضية
            'alerts': 1     # عدد التنبيهات المجانية الافتراضية
        }
    return usage.to_dict()
```

### 3️⃣ التعامل مع النسخ الاحتياطي
```python
def create_backup():
    # إنشاء مفتاح تشفير جديد
    key = Fernet.generate_key()
    
    # حفظ المفتاح في Firestore
    backup_ref = db.collection('backup_keys').document()
    backup_ref.set({
        'key': key.decode(),
        'created_at': firestore.SERVER_TIMESTAMP
    })
    
    return key
```

## 📝 ملاحظات مهمة

1. احرص على عدم مشاركة `serviceAccountKey.json` علناً
2. قم بإضافة الملف إلى `.gitignore`
3. استخدم متغيرات بيئية للمعلومات الحساسة
4. قم بعمل نسخ احتياطية دورية للبيانات
5. راقب استهلاك الموارد لتجنب التكاليف غير المتوقعة

## 🆘 استكشاف الأخطاء وإصلاحها

- **خطأ في الاتصال**: تأكد من صحة مفتاح الخدمة وتكوين Firebase
- **خطأ في الصلاحيات**: راجع قواعد الأمان في Firestore
- **بطء في الاستجابة**: تحقق من موقع قاعدة البيانات وتحسين الاستعلامات

## 🔗 روابط مفيدة

- [توثيق Firebase الرسمي](https://firebase.google.com/docs)
- [توثيق Admin SDK](https://firebase.google.com/docs/admin/setup)
- [أفضل ممارسات Firestore](https://firebase.google.com/docs/firestore/best-practices) 