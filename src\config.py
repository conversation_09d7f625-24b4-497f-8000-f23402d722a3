"""
ملف التكوين للبوت
"""

import os
from dotenv import load_dotenv

# تحميل متغيرات البيئة من ملف .env
load_dotenv()

# معلومات البوت
BOT_TOKEN = os.getenv('BOT_TOKEN')
# تم تثبيت معرف المطور ليكون دائماً 7839527436 (نفس قيمة SystemConfig.DEVELOPER_ID في main.py)
DEVELOPER_ID = '7839527436'  # استخدام نفس الاسم كما في SystemConfig
OWNER_ID = DEVELOPER_ID  # للتوافق مع الكود القديم

# معلومات API - تم إزالة المفاتيح الافتراضية
DEFAULT_BINANCE_API_KEY = None
DEFAULT_BINANCE_API_SECRET = None
DEFAULT_GEMINI_API_KEY = None

# إعدادات API المستخدم
REQUIRE_API_KEYS_FOR_AI_ANALYSIS = True  # إلزام المستخدمين بتوفير مفاتيح API للتحليل بالذكاء الاصطناعي
REQUIRE_API_KEYS_FOR_MARKET_DATA = True  # إلزام المستخدمين بتوفير مفاتيح API للحصول على بيانات السوق

# إعدادات الميزات المتقدمة
ENABLE_ADVANCED_FEATURES = True  # تمكين الميزات المتقدمة
ADVANCED_FEATURES = {
    'trading_strategy': True,  # استراتيجيات تداول آلية
    'price_prediction': True,  # تنبؤات سعرية
    'multi_timeframe': True    # تحليل متعدد الإطارات الزمنية
}

# معلومات PayPal
PAYPAL_CLIENT_ID = os.getenv('PAYPAL_CLIENT_ID')
PAYPAL_CLIENT_SECRET = os.getenv('PAYPAL_CLIENT_SECRET')
PAYPAL_SANDBOX_MODE = os.getenv('PAYPAL_SANDBOX_MODE', 'True').lower() == 'true'  # استخدام بيئة الاختبار
PAYPAL_LINK = os.getenv('PAYPAL_LINK')  # رابط الدفع اليدوي (سيتم استبداله بالرابط الآلي)

# معلومات التشفير
ENCRYPTION_KEY = os.getenv('ENCRYPTION_KEY')

# معلومات GitHub
GITHUB_TOKEN = os.getenv('GITHUB_TOKEN')
GITHUB_REPO = os.getenv('GITHUB_REPO')
GITHUB_OWNER = os.getenv('GITHUB_OWNER')

# إعدادات الاشتراك
SUBSCRIPTION_PRICE = 5.0  # USD
SUBSCRIPTION_DURATION = 7  # أيام

# إعدادات التخزين المؤقت
CACHE_TIMEOUT = 3600  # ثانية

# إعدادات التحليل
MAX_FREE_ANALYSES = 3  # عدد التحليلات المجانية يوميًا
MAX_FREE_ALERTS = 1  # عدد التنبيهات المجانية

# إعدادات API المستخدم
ENABLE_USER_API = True  # تمكين استخدام API المستخدم
