# -*- coding: utf-8 -*-
import logging
import google.generativeai as genai
import re # Ensure re is imported
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, Poll
import telegram
from telegram.ext import ContextTypes, CallbackContext
from telegram.constants import ChatAction, ParseMode
from firebase_admin import firestore
from utils import get_text, fix_bold_formatting # Assuming get_text is in utils.py for translations

# إعداد المسجل
logger = logging.getLogger(__name__)
# db = firestore.client() # إزالة هذه السطر
db = None # تعريف متغير db كـ None مبدئيًا

# دالة لتعيين كائن قاعدة البيانات
def set_firestore_db(firestore_db):
    global db
    db = firestore_db
    logger.info("Firestore DB set for trading_education module.")

# قاموس لتخزين حالة المستخدم التعليمية (يمكن نقله إلى Firestore لاحقًا)
user_education_state = {}

# تعريف دالة الترجمة المختصرة
def _(text, lang='ar', **kwargs):
    """اختصار لدالة get_text"""
    return get_text(text, lang, **kwargs)

# تعريف فصول الدورة التعليمية
CHAPTERS = {
    1: {
        'topic': {
            'ar': 'مقدمة في التداول',
            'en': 'Introduction to Trading'
        }
    },
    2: {
        'topic': {
            'ar': 'أساسيات التحليل الفني',
            'en': 'Basics of Technical Analysis'
        }
    },
    3: {
        'topic': {
            'ar': 'أساسيات التحليل الأساسي',
            'en': 'Basics of Fundamental Analysis'
        }
    },
    4: {
        'topic': {
            'ar': 'إدارة المخاطر',
            'en': 'Risk Management'
        }
    },
    5: {
        'topic': {
            'ar': 'استراتيجيات التداول',
            'en': 'Trading Strategies'
        }
    },
    6: {
        'topic': {
            'ar': 'علم النفس في التداول',
            'en': 'Trading Psychology'
        }
    },
    7: {
        'topic': {
            'ar': 'أدوات ومؤشرات التداول',
            'en': 'Trading Tools and Indicators'
        }
    },
    8: {
        'topic': {
            'ar': 'أنماط الرسوم البيانية',
            'en': 'Chart Patterns'
        }
    },
    9: {
        'topic': {
            'ar': 'التداول الآلي والخوارزميات',
            'en': 'Automated Trading and Algorithms'
        }
    },
    10: {
        'topic': {
            'ar': 'بناء خطة تداول شخصية',
            'en': 'Building a Personal Trading Plan'
        }
    }
}

# --- Helper Function for Language ---
async def get_user_language(user_id: str) -> str:
    """Fetches the user's preferred language from Firestore."""
    if not db:
        logger.error("Firestore DB not initialized in trading_education.")
        return 'ar' # Fallback language
    try:
        user_ref = db.collection('users').document(user_id)
        user_doc = user_ref.get() # إزالة await من هنا
        if user_doc.exists:
            lang = user_doc.to_dict().get('language', 'ar') # Default to Arabic if not set
            logger.debug(f"Language for user {user_id}: {lang}")
            return lang
        else:
            logger.warning(f"User document not found for {user_id}, defaulting to Arabic.")
            return 'ar'
    except Exception as e:
        logger.error(f"Error fetching language for user {user_id}: {e}")
        return 'ar' # Fallback to Arabic on error

# --- Translation Helper (Simplified) ---
# Moved to utils.py and imported as get_text
# translations = {
#     'ar': {
#         'welcome_trading_ai': "أهلاً بك في دورة تعلم التداول بالذكاء الاصطناعي! 📈",
#         'add_gemini_key_prompt': "للبدء، يرجى إضافة مفتاح Gemini API الخاص بك. هذا سيسمح لي بتوليد محتوى تعليمي مخصص لك.",
#         'add_gemini_key_button': "🔑 إضافة مفتاح Gemini API",
#         'gemini_access_error': "عذرًا، لا يمكنني الوصول إلى Gemini الآن. يرجى التأكد من إضافة مفتاح API صالح.",
#         'gemini_tutor_error': "حدث خطأ أثناء محاولة الإجابة على سؤالك. يرجى المحاولة مرة أخرى لاحقًا.",
#         'lesson_generation_error': "عذرًا، لا يمكنني الوصول إلى Gemini الآن لتوليد الدرس. يرجى التأكد من إضافة مفتاح API صالح.",
#         'chapter_button': "الفصل {number}",
#         'next_chapter_button': "الفصل التالي ❯",
#         'take_quiz_button': "📝 إجراء الاختبار",
#         'ask_tutor_prompt': "يمكنك أيضًا طرح أي سؤال يتعلق بالتداول وسأبذل قصارى جهدي للإجابة عليه باستخدام Gemini.",
#         'quiz_already_started': "📝 يبدو أنك بدأت الاختبار بالفعل. يرجى إكمال الإجابة على الأسئلة.",
#         'course_completed': "لقد أكملت الدورة الأساسية والاختبار! 🎉",
#         'error_starting_course': "حدث خطأ ما. يرجى محاولة بدء الدورة مرة أخرى باستخدام /learn_trading_ai",
#         'tutor_prompt_header': "أجب على سؤال التداول التالي كمدرس خبير باللغة {lang}:\n\nالسؤال: {question}\n\nالإجابة:",
#         'chapter_gen_prompt_header': "أنت مدرس تداول خبير ومؤلف محتوى تعليمي باللغة {{lang}}. مهمتك هي إنشاء محتوى تعليمي للفصل رقم {{chapter_number}} من دورة تداول للمبتدئين مكونة من 10 فصول. ",
#         # ... add other keys as needed
#     },
#     'en': {
#         'welcome_trading_ai': "Welcome to the AI Trading Learning Course! 📈",
#         'add_gemini_key_prompt': "To get started, please add your Gemini API key. This will allow me to generate personalized educational content for you.",
#         'add_gemini_key_button': "🔑 Add Gemini API Key",
#         'gemini_access_error': "Sorry, I can't access Gemini right now. Please ensure you have added a valid API key.",
#         'gemini_tutor_error': "An error occurred while trying to answer your question. Please try again later.",
#         'lesson_generation_error': "Sorry, I can't access Gemini right now to generate the lesson. Please ensure you have added a valid API key.",
#         'chapter_button': "Chapter {number}",
#         'next_chapter_button': "Next Chapter ❯",
#         'take_quiz_button': "📝 Take the Quiz",
#         'ask_tutor_prompt': "You can also ask any trading-related question, and I'll do my best to answer it using Gemini.",
#         'quiz_already_started': "📝 It looks like you've already started the quiz. Please complete answering the questions.",
#         'course_completed': "You have completed the basic course and the quiz! 🎉",
#         'error_starting_course': "Something went wrong. Please try starting the course again using /learn_trading_ai",
#         'tutor_prompt_header': "Answer the following trading question as an expert tutor in {lang}:\n\nQuestion: {question}\n\nAnswer:",
#         'chapter_gen_prompt_header': "You are an expert trading tutor and educational content creator in {lang}. Your task is to create educational content for chapter {chapter_number} of a 10-chapter beginner trading course.",
#         # ... add other keys as needed
#     }
# }

# def get_text(key: str, lang: str = 'ar', **kwargs) -> str:
#     """Gets translated text, defaulting to Arabic."""
#     return translations.get(lang, translations['ar']).get(key, f"[{key}]").format(**kwargs)


async def handle_learn_trading_ai(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handles the /learn_trading_ai command."""
    user_id = str(update.effective_user.id)
    lang = await get_user_language(user_id)
    logger.info(f"User {user_id} (lang: {lang}) started learning trading with AI.")

    # 1. Check Gemini API Key
    gemini_api_key = await check_gemini_api_key(user_id)
    if not gemini_api_key:
        keyboard = [[InlineKeyboardButton(get_text('add_gemini_key_button', lang), callback_data='add_gemini_key')]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        await update.message.reply_text(
            f"{get_text('welcome_trading_ai', lang)}\n\n"
            f"{get_text('add_gemini_key_prompt', lang)}",
            reply_markup=reply_markup
        )
        return

    # 2. Initialize user state if not exists
    if user_id not in user_education_state:
        user_education_state[user_id] = {'current_chapter': 0, 'quiz_taken': False, 'quiz_score': None, 'needs_review': []}
        logger.info(f"Initialized education state for user {user_id}")

    # 3. Show welcome message and start/continue course or allow questions
    await start_or_continue_lesson(update, context)

async def check_gemini_api_key(user_id: str) -> str | None:
    """Checks if Gemini API key exists and returns it if it exists."""
    # TODO: Implement check for Gemini API key (maybe via private chat or another command)
    # Currently, we just check Gemini API key validity
    # في الوقت الحالي، سنفترض وجود مفتاح افتراضي أو أن المستخدم قد أضافه
    # يجب استبدال هذا بمنطق حقيقي للتحقق من Firestore
    try:
        from analysis.gemini_analysis import get_user_api_client
        model = await get_user_api_client(user_id, 'gemini')
        if model:
             logger.info(f"Found Gemini API key for user {user_id}")
             # لا نعيد المفتاح نفسه، فقط نتأكد من وجوده وتهيئته
             return "valid_key_placeholder" # مجرد مؤشر على وجود مفتاح صالح
        else:
            logger.warning(f"Didn't find Gemini API key for user {user_id}")
            return None
    except Exception as e:
        logger.error(f"Error checking Gemini API key for user {user_id}: {e}")
        return None

async def get_gemini_api_for_user(user_id: str):
    """الحصول على واجهة برمجة تطبيقات Gemini للمستخدم"""
    try:
        from analysis.gemini_analysis import get_user_api_client
        model = await get_user_api_client(user_id, 'gemini')
        if model:
            logger.info(f"Successfully got Gemini API client for user {user_id}")
            return model
        else:
            logger.warning(f"Failed to get Gemini API client for user {user_id}")
            return None
    except Exception as e:
        logger.error(f"Error getting Gemini API client for user {user_id}: {e}")
        return None

async def handle_message_for_ai_tutor(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handles user messages directed to the AI tutor."""
    user_id = str(update.effective_user.id)
    user_message = update.message.text
    lang = await get_user_language(user_id)

    if user_id not in user_education_state or not user_education_state[user_id].get('is_asking_ai', False):
        # If the user is not in learning mode or not in asking AI mode, ignore or redirect
        # await update.message.reply_text(get_text('start_learning_first', lang))
        return

    logger.info(f"User {user_id} (lang: {lang}) asks AI tutor: {user_message}")

    # Call Gemini to answer the user's question
    try:
        from analysis.gemini_analysis import get_user_api_client
        model = await get_user_api_client(user_id, 'gemini')
        if not model:
            await update.message.reply_text(get_text('gemini_access_error', lang))
            return

        # Build conversation context (if needed)
        # conversation_history = user_education_state[user_id].get('conversation', [])
        # prompt = build_prompt_with_history(user_message, conversation_history, lang)

        # الحصول على معلومات الفصل الحالي للمستخدم
        current_chapter = user_education_state[user_id].get('current_chapter', 1)
        chapter_info = CHAPTERS.get(current_chapter, {})
        chapter_topic = chapter_info.get('topic', {}).get(lang, 'موضوع غير محدد')

        # إنشاء سياق للسؤال بناءً على الفصل الحالي
        prompt = (
            f"أنت مدرس خبير في التداول. المستخدم يدرس حاليًا الفصل {current_chapter} حول موضوع '{chapter_topic}'. "
            f"أجب على سؤاله التالي باللغة {lang} مع التركيز على المفاهيم المتعلقة بهذا الموضوع قدر الإمكان:\n\n"
            f"السؤال: {user_message}\n\n"
            f"الإجابة:"
        )

        response = await model.generate_content_async(prompt)
        ai_response = response.text
        logger.info(f"تم الحصول على استجابة من Gemini بطول {len(ai_response)} حرف")

        # التحقق من طول الرد - إذا كان قصيرًا جدًا، نعيد المحاولة
        min_response_length = 100  # الحد الأدنى المقبول لطول الرد
        if len(ai_response) < min_response_length:
            logger.warning(f"استجابة Gemini قصيرة جدًا ({len(ai_response)} حرف). محاولة إعادة الاستدعاء مع توجيهات إضافية.")

            # إضافة توجيهات إضافية للحصول على رد أكثر تفصيلاً
            enhanced_prompt = prompt + "\n\n"
            if lang == 'ar':
                enhanced_prompt += """
يرجى تقديم إجابة مفصلة ومتكاملة. الإجابة القصيرة جدًا غير مقبولة. يجب أن تتضمن إجابتك:
1. شرحًا مفصلاً للمفاهيم المتعلقة بالسؤال
2. أمثلة عملية توضح الفكرة
3. نصائح وإرشادات للمتداولين المبتدئين
4. معلومات إضافية مفيدة متعلقة بالموضوع

يجب أن تكون الإجابة شاملة وتغطي جميع جوانب السؤال بعمق.
"""
            else:
                enhanced_prompt += """
Please provide a detailed and comprehensive answer. Very short responses are not acceptable. Your answer must include:
1. Detailed explanation of concepts related to the question
2. Practical examples illustrating the idea
3. Tips and guidance for beginner traders
4. Additional useful information related to the topic

The answer should be comprehensive and cover all aspects of the question in depth.
"""

            # إعادة استدعاء النموذج مع التوجيهات المحسنة
            logger.info("إعادة استدعاء نموذج Gemini مع توجيهات إضافية")
            try:
                response = await model.generate_content_async(enhanced_prompt)
                ai_response = response.text
                logger.info(f"تم الحصول على استجابة محسنة من Gemini بطول {len(ai_response)} حرف")
            except Exception as retry_error:
                logger.error(f"فشل في إعادة استدعاء Gemini API: {str(retry_error)}")
                # نستمر باستخدام الاستجابة الأصلية

        # Update conversation history (if needed)
        # user_education_state[user_id]['conversation'].append({'user': user_message, 'ai': ai_response})

        await update.message.reply_text(ai_response)

        # إعادة تعيين حالة المستخدم بعد الإجابة على السؤال
        # تم نقل إعادة التعيين إلى كتلة finally لضمان تنفيذها حتى في حالة حدوث خطأ

    except Exception as e:
        logger.error(f"Error processing user {user_id} question with Gemini: {e}")
        await update.message.reply_text(get_text('gemini_tutor_error', lang))

        # تم نقل إعادة التعيين إلى كتلة finally
        pass
    finally:
        # إعادة تعيين حالة المستخدم بعد الإجابة على السؤال أو في حالة حدوث خطأ
        if user_id in user_education_state:
            user_education_state[user_id]['is_asking_ai'] = False
            # إضافة زر للسماح للمستخدم بطرح سؤال آخر
            try:
                # إنشاء أزرار التنقل
                keyboard = []

                # إضافة زر الفصل التالي إذا كان المستخدم في فصل أقل من 10
                current_chapter = user_education_state[user_id].get('current_chapter', 1)
                if current_chapter < 10:
                    keyboard.append([InlineKeyboardButton(get_text('next_chapter_button', lang, default="الفصل التالي ❯"),
                                                        callback_data=f'next_chapter_{current_chapter + 1}')])

                # إضافة زر لطرح سؤال آخر
                keyboard.append([InlineKeyboardButton(get_text('ask_tutor_button', lang, default="❓ اسأل مدرس الذكاء الاصطناعي"),
                                                    callback_data='ask_ai_tutor')])

                # إرسال الرسالة مع الأزرار
                await update.message.reply_text(
                    get_text('ask_again_prompt', lang, default="يمكنك طرح سؤال آخر أو الانتقال إلى الفصل التالي."),
                    reply_markup=InlineKeyboardMarkup(keyboard)
                )
            except Exception as e:
                logger.error(f"Error sending navigation buttons for user {user_id}: {e}")

async def start_or_continue_lesson(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Starts the first lesson or continues the current lesson for the user."""
    user_id = str(update.effective_user.id)
    lang = await get_user_language(user_id)
    state = user_education_state.get(user_id)

    if not state:
        logger.warning(f"Educational state not found for user {user_id} to start lesson.")
        message_target = update.callback_query.message if update.callback_query else update.message
        await message_target.reply_text(get_text('error_starting_course', lang))
        return

    current_chapter = state['current_chapter']

    if current_chapter == 0:
        # Start the first lesson
        await generate_and_send_chapter(update, context, user_id, 1, lang)
    elif current_chapter < 10:
        # Resume current lesson or move to the next (if button pressed)
        callback_data = update.callback_query.data if update.callback_query else None
        next_chapter_match = re.match(r'next_chapter_(\d+)', callback_data) if callback_data else None
        requested_chapter = int(next_chapter_match.group(1)) if next_chapter_match else current_chapter + 1

        # التحقق من أن الفصل المطلوب ليس أقل من أو يساوي الفصل الحالي
        if requested_chapter <= current_chapter:
            # إذا كان المستخدم يحاول الانتقال إلى فصل سابق أو الفصل الحالي، نرسل رسالة توضيحية
            message_target = update.callback_query.message if update.callback_query else update.message
            await message_target.reply_text(get_text('already_completed_chapter', lang, default=f"لقد أكملت بالفعل الفصل {requested_chapter}. أنت الآن في الفصل {current_chapter}."))
            return

        # التحقق من أن الفصل المطلوب هو الفصل التالي مباشرة
        if requested_chapter > current_chapter + 1:
            # إذا كان المستخدم يحاول تخطي فصل، نرسل رسالة توضيحية
            message_target = update.callback_query.message if update.callback_query else update.message
            await message_target.reply_text(get_text('must_complete_previous_chapter', lang, default=f"يجب عليك إكمال الفصل {current_chapter + 1} أولاً قبل الانتقال إلى الفصل {requested_chapter}."))
            # ثم ننتقل إلى الفصل التالي مباشرة
            await generate_and_send_chapter(update, context, user_id, current_chapter + 1, lang)
            return

        # إذا كان الفصل المطلوب هو الفصل التالي مباشرة، ننتقل إليه
        await generate_and_send_chapter(update, context, user_id, requested_chapter, lang)

    elif current_chapter == 10 and not state.get('quiz_started', False):
        # Start the quiz if not already started
        await start_quiz(update, context, user_id, lang)
    elif state.get('quiz_completed', False):
        # Show quiz results or further lessons
        await show_quiz_results_or_next_steps(update, context, user_id, lang)
    elif state.get('quiz_started', False) and not state.get('quiz_completed', False):
        # If quiz started but not completed, send a reminder
        message_target = update.callback_query.message if update.callback_query else update.message
        await message_target.reply_text(get_text('quiz_already_started', lang))
    else:
         message_target = update.callback_query.message if update.callback_query else update.message
         await message_target.reply_text(get_text('course_completed', lang))
         # More options can be added here

async def generate_and_send_chapter(update: Update, context: ContextTypes.DEFAULT_TYPE, user_id: str, chapter_number: int, lang: str = 'ar'):
    """Generates chapter content and sends it to the user."""
    logger.info(f"Generating chapter {chapter_number} for user {user_id} in {lang}")
    state = user_education_state.get(user_id)
    if not state:
        return

    try:
        from analysis.gemini_analysis import get_user_api_client
        model = await get_user_api_client(user_id, 'gemini')
        if not model:
            message_target = update.callback_query.message if update.callback_query else update.message
            await message_target.reply_text(get_text('lesson_generation_error', lang))
            return

        # Check if chapter number is within the allowed range (1-10)
        if not 1 <= chapter_number <= 10:
            logger.warning(f"Attempt to generate chapter outside range: {chapter_number}")
            if chapter_number > 10 and not state.get('quiz_taken', False):
                await start_quiz(update, context, user_id, lang)
            else:
                await show_quiz_results_or_next_steps(update, context, user_id, lang)
            return

        # Build prompt dynamically for topic and content generation in the specified language
        prompt = (
            f"أنت مدرس تداول خبير ومؤلف محتوى تعليمي باللغة {{lang}}. مهمتك هي إنشاء محتوى تعليمي للفصل رقم {{chapter_number}} استخدم ايموجي بكثرة داخل المحتوى بشكل يتناسب مع المحتوى من دورة تداول للمبتدئين مكونة من 10  فصول. "
            f"**تعليمات صارمة:** قم بإنشاء المحتوى المطلوب فقط بالتنسيق المحدد أدناه. لا تطرح أسئلة أو تبدأ حوارًا. ركز فقط على توليد المحتوى التعليمي."
            f"\n\nالرجاء القيام بما يلي:\n"
            f"1.  **تحديد موضوع مناسب:** اقترح موضوعًا تعليميًا أصليًا ومناسبًا لهذا الفصل ({{chapter_number}}/10) في سياق دورة تداول للمبتدئين. يجب أن يكون الموضوع أكثر تقدمًا قليلاً من الفصل السابق (إذا كان chapter_number > 1) وأن يمهد للفصل التالي (إذا كان chapter_number < 10). تجنب تكرار الموضوعات التي يُحتمل تغطيتها في فصول أخرى بشكل مباشر.\n"
            f"2.  **إنشاء محتوى الفصل:** اكتب المحتوى التعليمي للموضوع المختار باللغة {{lang}}. يجب أن يكون المحتوى واضحًا وموجزًا وجذابًا ومناسبًا للمبتدئين. استخدم تنسيق markdown (التغميق، القوائم، إلخ) لتحسين القراءة. استهدف طولًا معقولًا (على سبيل المثال، 200-400 كلمة).\n"
            f"3.  **تقديم ملخص موجز:** أضف ملخصًا قصيرًا (1-2 جملة) في النهاية.\n"
            f"4.  **تنسيق الإخراج:** قم بتنظيم استجابتك بوضوح، بدءًا بالموضوع المقترح، متبوعًا بالمحتوى، ومنتهيًا بالملخص. استخدم عناوين أو فواصل واضحة. **يجب أن تلتزم بهذا التنسيق بالضبط.**\n"
            f"\nمثال على الهيكل المطلوب:\n"
            f"**الموضوع:** [الموضوع المقترح هنا]\n\n"
            f"[محتوى الفصل هنا باستخدام Markdown]\n\n"
            f"**الملخص:** [ملخص موجز هنا]"
        ).format(lang=lang, chapter_number=chapter_number) # استخدام .format() لتجنب مشاكل f-string المتداخلة

        response = await model.generate_content_async(prompt)
        chapter_content_full = response.text
        logger.info(f"تم الحصول على استجابة من Gemini بطول {len(chapter_content_full)} حرف")

        # التحقق من طول الرد - إذا كان قصيرًا جدًا، نعيد المحاولة
        min_response_length = 200  # الحد الأدنى المقبول لطول الرد للفصل
        if len(chapter_content_full) < min_response_length:
            logger.warning(f"استجابة Gemini قصيرة جدًا ({len(chapter_content_full)} حرف). محاولة إعادة الاستدعاء مع توجيهات إضافية.")

            # إضافة توجيهات إضافية للحصول على رد أكثر تفصيلاً
            enhanced_prompt = prompt + "\n\n"
            if lang == 'ar':
                enhanced_prompt += """
يرجى تقديم محتوى تعليمي مفصل ومتكامل. المحتوى القصير جدًا غير مقبول. يجب أن يتضمن المحتوى:
1. شرحًا مفصلاً للمفاهيم الأساسية في هذا الفصل
2. أمثلة عملية توضح المفاهيم
3. نصائح وإرشادات للمتداولين المبتدئين
4. معلومات إضافية مفيدة متعلقة بالموضوع
5. استخدام الرموز التعبيرية (الإيموجي) بشكل مناسب

يجب أن يكون المحتوى شاملاً ويغطي جميع جوانب الموضوع بعمق، مع الالتزام بالتنسيق المطلوب.
"""
            else:
                enhanced_prompt += """
Please provide detailed and comprehensive educational content. Very short content is not acceptable. Your content must include:
1. Detailed explanation of the core concepts in this chapter
2. Practical examples illustrating the concepts
3. Tips and guidance for beginner traders
4. Additional useful information related to the topic
5. Appropriate use of emojis

The content should be comprehensive and cover all aspects of the topic in depth, while following the requested format.
"""

            # إعادة استدعاء النموذج مع التوجيهات المحسنة
            logger.info("إعادة استدعاء نموذج Gemini مع توجيهات إضافية")
            try:
                response = await model.generate_content_async(enhanced_prompt)
                chapter_content_full = response.text
                logger.info(f"تم الحصول على استجابة محسنة من Gemini بطول {len(chapter_content_full)} حرف")
            except Exception as retry_error:
                logger.error(f"فشل في إعادة استدعاء Gemini API: {str(retry_error)}")
                # نستمر باستخدام الاستجابة الأصلية

        # --- Extract Topic, Content, Summary (Example - needs refinement based on Gemini's actual output format) ---
        topic = f"{get_text('chapter_button', lang, number=chapter_number)}"
        content = chapter_content_full # Default to full response if parsing fails
        summary = ""

        try:
            # Attempt to parse based on the example structure requested in the prompt
            # Mejorar las expresiones regulares para que sean más robustas
            topic_match = re.search(r'\*\*(?:الموضوع|Topic):\*\*\s*(.*)', chapter_content_full, re.MULTILINE | re.IGNORECASE)
            summary_match = re.search(r'\*\*(?:الملخص|Summary):\*\*\s*(.*?)$', chapter_content_full, re.MULTILINE | re.DOTALL | re.IGNORECASE)

            if topic_match and summary_match:
                topic = topic_match.group(1).strip()
                # Extract content between topic and summary
                content_start = topic_match.end()
                content_end = summary_match.start()
                content = chapter_content_full[content_start:content_end].strip()
                # Remove the summary line itself from the content if it was captured
                content = re.sub(r'\*\*(?:الملخص|Summary):\*\*.*?$', '', content, flags=re.MULTILINE | re.DOTALL | re.IGNORECASE).strip()
                summary = summary_match.group(1).strip()
            elif topic_match: # If only topic is found
                topic = topic_match.group(1).strip()
                content_start = topic_match.end()
                content = chapter_content_full[content_start:].strip()
            else:
                 logger.warning(f"Could not parse topic/summary from Gemini response for chapter {chapter_number}, user {user_id}. Using full response.")

        except Exception as parse_error:
            logger.error(f"Error parsing Gemini response for chapter {chapter_number}, user {user_id}: {parse_error}. Using full response.")
            content = chapter_content_full # Fallback

        # --- Send Chapter Content ---
        message_target = update.callback_query.message if update.callback_query else update.message
        # إصلاح تنسيق النص العريض (إزالة النقطتين من داخل النص العريض)
        content = fix_bold_formatting(content)
        await message_target.reply_text(f"📚 **{topic}** ({chapter_number}/10)\n\n{content}")

        # Update user state
        state['current_chapter'] = chapter_number
        state['quiz_taken'] = False # Reset quiz status when moving to a new chapter
        state['quiz_score'] = None
        state['needs_review'] = []
        state['quiz_started'] = False
        state['quiz_completed'] = False
        logger.info(f"Sent chapter {chapter_number} to user {user_id}. State updated.")

        # --- Send Navigation Buttons ---
        keyboard = []
        if chapter_number < 10:
            keyboard.append([InlineKeyboardButton(get_text('next_chapter_button', lang), callback_data=f'next_chapter_{chapter_number + 1}')])
        else:
            # Last chapter, offer quiz
            keyboard.append([InlineKeyboardButton(get_text('take_quiz_button', lang), callback_data='start_quiz')])

        # Add a button to ask the tutor
        keyboard.append([InlineKeyboardButton(get_text('ask_tutor_button', lang, default='❓ Ask AI Tutor'), callback_data='ask_ai_tutor')]) # Added default

        reply_markup = InlineKeyboardMarkup(keyboard)
        # Send the buttons as a new message or edit the previous one if possible
        # Sending as a new message for simplicity here
        await message_target.reply_text(get_text('ask_tutor_prompt', lang), reply_markup=reply_markup)

    except Exception as e:
        logger.error(f"Error generating/sending chapter {chapter_number} for user {user_id}: {e}")
        message_target = update.callback_query.message if update.callback_query else update.message
        await message_target.reply_text(get_text('lesson_generation_error', lang))

# --- Quiz Functions with Poll Support ---
async def start_quiz(update: Update, context: ContextTypes.DEFAULT_TYPE, user_id: str, lang: str):
    """بدء الاختبار باستخدام استطلاعات الرأي (poll)"""
    logger.info(f"Starting quiz for user {user_id} in {lang}")
    state = user_education_state.get(user_id)
    if not state or state.get('quiz_started'):
        return # تجنب إعادة بدء الاختبار إذا كان قد بدأ بالفعل

    # تهيئة حالة الاختبار
    state['quiz_started'] = True
    state['quiz_completed'] = False
    state['current_question_index'] = 0
    state['user_answers'] = []
    state['correct_answers'] = 0
    state['total_questions'] = 0

    # حفظ chat_id في حالة المستخدم
    chat_id = None
    if update.callback_query and update.callback_query.message:
        chat_id = update.callback_query.message.chat_id
    elif update.message:
        chat_id = update.message.chat_id
    elif update.effective_chat:
        chat_id = update.effective_chat.id

    if chat_id:
        state['chat_id'] = chat_id

    # إرسال رسالة بدء الاختبار
    message_target = update.callback_query.message if update.callback_query else update.message
    wait_message = await message_target.reply_text(get_text('generating_quiz', lang, default="⏳ جاري إنشاء الاختبار..."))

    # إنشاء أسئلة الاختبار باستخدام Gemini
    quiz_questions = await generate_quiz_questions(user_id, lang)

    if not quiz_questions or len(quiz_questions) == 0:
        # في حالة فشل إنشاء الأسئلة، نرسل رسالة خطأ ونعيد تعيين حالة الاختبار
        await wait_message.edit_text(get_text('quiz_generation_error', lang, default="حدث خطأ أثناء إنشاء أسئلة الاختبار. يرجى المحاولة مرة أخرى لاحقًا."))
        state['quiz_started'] = False
        return

    # تخزين أسئلة الاختبار في حالة المستخدم
    state['quiz_questions'] = quiz_questions
    state['total_questions'] = len(quiz_questions)

    # إرسال رسالة بدء الاختبار
    await wait_message.edit_text(
        get_text('quiz_starting', lang, default="🎯 بدء الاختبار!\n\nسيتم إرسال الأسئلة واحدًا تلو الآخر. لديك 30 ثانية للإجابة على كل سؤال.")
    )

    # انتظار لحظة قبل إرسال السؤال الأول
    import asyncio
    await asyncio.sleep(2)

    # إرسال السؤال الأول
    await send_quiz_question(update, context, user_id, lang, 0)


async def show_quiz_results_or_next_steps(update: Update, context: ContextTypes.DEFAULT_TYPE, user_id: str, lang: str):
    """عرض نتائج الاختبار والخطوات التالية"""
    logger.info(f"Showing quiz results/next steps for user {user_id} in {lang}")
    state = user_education_state.get(user_id)
    if not state:
        logger.warning(f"User state not found for {user_id}")
        return

    # التحقق من اكتمال الاختبار
    if not state.get('quiz_completed') and not state.get('quiz_taken'):
        logger.warning(f"Quiz not completed for user {user_id}")
        # إعادة توجيه المستخدم إلى الاختبار إذا لم يكن قد أكمله
        message_target = update.callback_query.message if update.callback_query else update.message
        await message_target.reply_text(get_text('quiz_not_completed', lang, default="لم تكمل الاختبار بعد. هل ترغب في بدء الاختبار الآن؟"))

        # إضافة زر لبدء الاختبار
        keyboard = [[InlineKeyboardButton(get_text('take_quiz_button', lang), callback_data='start_quiz')]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        await message_target.reply_text(get_text('quiz_prompt', lang, default="انقر على الزر أدناه لبدء الاختبار:"), reply_markup=reply_markup)
        return

    # حساب النتيجة
    score = state.get('quiz_score', 0)
    total_questions = state.get('total_questions', 0) or len(state.get('quiz_questions', []))

    if total_questions == 0:
        logger.warning(f"No quiz questions found for user {user_id}")
        message_target = update.callback_query.message if update.callback_query else update.message
        await message_target.reply_text(get_text('quiz_error', lang, default="حدث خطأ في الاختبار. يرجى المحاولة مرة أخرى لاحقًا."))
        return

    # حساب النسبة المئوية
    percentage = (score / total_questions) * 100

    # تحديد مستوى الأداء
    if percentage >= 90:
        performance_text = get_text('quiz_excellent', lang, default="ممتاز! 🌟")
        emoji = "🏆"
    elif percentage >= 75:
        performance_text = get_text('quiz_very_good', lang, default="جيد جدًا! 👏")
        emoji = "🎯"
    elif percentage >= 60:
        performance_text = get_text('quiz_good', lang, default="جيد! 👍")
        emoji = "👍"
    elif percentage >= 40:
        performance_text = get_text('quiz_fair', lang, default="مقبول. 🤔")
        emoji = "📚"
    else:
        performance_text = get_text('quiz_needs_improvement', lang, default="تحتاج إلى مزيد من الدراسة. 📖")
        emoji = "📖"

    # إنشاء رسالة النتيجة
    result_message = f"{emoji} **{get_text('quiz_results', lang, default='نتائج الاختبار')}** {emoji}\n\n"
    result_message += f"**{get_text('quiz_score', lang, default='النتيجة')}**: {score}/{total_questions} ({percentage:.1f}%)\n"
    result_message += f"**{get_text('quiz_performance', lang, default='التقييم')}**: {performance_text}\n\n"

    # إصلاح تنسيق النص العريض (إزالة النقطتين من داخل النص العريض)
    result_message = fix_bold_formatting(result_message)

    # إضافة تعليق حسب النتيجة
    if percentage >= 75:
        result_message += get_text('quiz_high_score_comment', lang, default="أحسنت! لقد أظهرت فهمًا ممتازًا لمفاهيم التداول. استمر في التعلم والتطبيق! 🚀")
    elif percentage >= 50:
        result_message += get_text('quiz_medium_score_comment', lang, default="جيد! لديك فهم أساسي للمفاهيم، لكن هناك مجال للتحسين. راجع المواضيع التي واجهت فيها صعوبة. 📈")
    else:
        result_message += get_text('quiz_low_score_comment', lang, default="لا بأس! التداول مجال معقد ويحتاج إلى وقت للإتقان. يُنصح بمراجعة الفصول مرة أخرى والتركيز على المفاهيم الأساسية. 💪")

    # إرسال رسالة النتيجة
    message_target = update.callback_query.message if update.callback_query else update.message
    await message_target.reply_text(result_message, parse_mode=ParseMode.MARKDOWN)

    # تحديد المواضيع التي تحتاج إلى مراجعة (إذا كانت النتيجة منخفضة)
    if percentage < 70 and 'user_answers' in state and 'quiz_questions' in state:
        # تحديد الأسئلة التي أجاب عليها المستخدم بشكل خاطئ
        incorrect_questions = []
        user_answers = state.get('user_answers', [])
        quiz_questions = state.get('quiz_questions', [])

        for i, (answer, question) in enumerate(zip(user_answers, quiz_questions)):
            if answer is None or answer != question.get('correct_option_id'):
                incorrect_questions.append(question)

        if incorrect_questions:
            # استخراج المواضيع من الأسئلة الخاطئة
            topics_to_review = [q.get('question', '').split(':')[0].strip() if ':' in q.get('question', '') else q.get('question', '') for q in incorrect_questions]

            # إنشاء مواد مراجعة
            review_material = await generate_review_material(user_id, lang, topics_to_review)

            # إرسال مواد المراجعة
            await message_target.reply_text(
                f"**{get_text('review_material_title', lang, default='مواد للمراجعة')}** 📚\n\n{review_material}",
                parse_mode=ParseMode.MARKDOWN
            )

    # إضافة أزرار للخطوات التالية
    keyboard = []

    # زر لإعادة الاختبار
    keyboard.append([InlineKeyboardButton(
        get_text('retake_quiz_button', lang, default="🔄 إعادة الاختبار"),
        callback_data='start_quiz'
    )])

    # زر للعودة إلى القائمة الرئيسية
    keyboard.append([InlineKeyboardButton(
        get_text('back_to_main_button', lang, default="🏠 العودة إلى القائمة الرئيسية"),
        callback_data='back_to_main'
    )])

    reply_markup = InlineKeyboardMarkup(keyboard)
    await message_target.reply_text(
        get_text('next_steps', lang, default="ماذا ترغب في فعله الآن؟"),
        reply_markup=reply_markup
    )

    # تحديث حالة المستخدم
    state['quiz_completed'] = True
    state['quiz_taken'] = True

# --- Callback Query Handlers (Need language awareness if text matters) ---

async def button_handler(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Parses the CallbackQuery and updates the message text."""
    query = update.callback_query
    await query.answer() # Acknowledge callback query
    user_id = str(query.from_user.id)
    lang = await get_user_language(user_id)

    logger.info(f"Button pressed by {user_id} (lang: {lang}): {query.data}")

    if query.data == 'add_gemini_key':
        # TODO: Implement logic to guide user to add key (maybe via private chat or another command)
        await query.edit_message_text(text=get_text('guide_add_gemini_key', lang, default="Please use the /add_gemini_key command to add your API key."))
    elif query.data.startswith('next_chapter_'):
        await start_or_continue_lesson(update, context)
    elif query.data == 'start_quiz':
         state = user_education_state.get(user_id)
         if state and state['current_chapter'] >= 10:
             await start_quiz(update, context, user_id, lang)
         else:
             await query.message.reply_text(get_text('complete_chapters_first', lang, default="Please complete all chapters before taking the quiz."))
    elif query.data == 'supplementary_chapters':
        # معالجة زر الفصول التكميلية
        await show_supplementary_chapters(update, context, user_id, lang)
    # تم نقل معالجة زر 'ask_ai_tutor' إلى دالة handle_trading_education_callback في ملف main.py
    # لذلك تم إزالة المعالجة من هنا لتجنب التعارض
    # Add handlers for quiz answers if using buttons
    # elif query.data.startswith('quiz_answer_'):
    #    await handle_quiz_answer(update, context, user_id, lang)
    elif query.data.startswith('supplementary_chapter_'):
        # معالجة اختيار فصل تكميلي محدد
        chapter_id = query.data.replace('supplementary_chapter_', '')
        await generate_and_send_supplementary_chapter(update, context, user_id, chapter_id, lang)
    elif query.data == 'back_to_quiz_results':
        # العودة إلى نتائج الاختبار
        await show_quiz_results_or_next_steps(update, context, user_id, lang)
    else:
        logger.warning(f"Unhandled button data: {query.data}")
        try:
            # Attempt to edit the message to remove buttons if the action is unknown/done
            await query.edit_message_reply_markup(reply_markup=None)
        except Exception as e:
            logger.debug(f"Could not remove buttons for unknown callback: {e}")

async def generate_quiz_questions(user_id: str, lang: str) -> list:
    """إنشاء أسئلة الاختبار باستخدام Gemini بناءً على محتوى الفصول"""
    try:
        logger.info(f"Generating quiz questions for user {user_id} in {lang}")

        # الحصول على نموذج Gemini للمستخدم
        gemini_api = await get_gemini_api_for_user(user_id)
        if not gemini_api:
            logger.warning(f"Gemini API not available for user {user_id} for quiz generation")
            return []

        # تحديد عدد الأسئلة في الاختبار
        num_questions = 20  # تم زيادة عدد الأسئلة من 5 إلى 20

        # بناء سياق الاختبار بناءً على الفصول التي درسها المستخدم
        chapters_studied = []
        state = user_education_state.get(user_id, {})
        current_chapter = state.get('current_chapter', 0)

        # جمع مواضيع الفصول التي درسها المستخدم
        for chapter_num in range(1, current_chapter + 1):
            chapter_info = CHAPTERS.get(chapter_num, {})
            topic = chapter_info.get('topic', {}).get(lang, f"الفصل {chapter_num}")
            chapters_studied.append(f"الفصل {chapter_num}: {topic}")

        # بناء المطالبة لإنشاء أسئلة الاختبار
        prompt = f"""
أنت مدرس خبير في التداول ومصمم اختبارات محترف. مهمتك هي إنشاء {num_questions} أسئلة اختبار من نوع الاختيار من متعدد باللغة {lang} لتقييم فهم المتعلم للمفاهيم التالية:

{chr(10).join(chapters_studied)}

**متطلبات الأسئلة:**
1. يجب أن تكون الأسئلة متنوعة وتغطي مفاهيم مختلفة من الفصول المذكورة أعلاه.
2. كل سؤال يجب أن يكون له 4 خيارات بالضبط، مع إجابة صحيحة واحدة فقط.
3. يجب أن تكون الأسئلة واضحة ومباشرة ومناسبة لمستوى المبتدئين.
4. يجب أن تكون الخيارات واقعية ومعقولة (لا تضع خيارات سخيفة أو غير منطقية).

**تنسيق الإخراج المطلوب:**
أنتج مصفوفة JSON تحتوي على {num_questions} عناصر، كل عنصر يمثل سؤالاً بالتنسيق التالي:
```
[
  {{
    "question": "نص السؤال هنا؟",
    "options": ["الخيار الأول", "الخيار الثاني", "الخيار الثالث", "الخيار الرابع"],
    "correct_option_id": 0  // رقم الخيار الصحيح (0-3)
  }},
  // المزيد من الأسئلة...
]
```

ملاحظة: تأكد من أن correct_option_id هو رقم صحيح يمثل فهرس الخيار الصحيح في مصفوفة options (0 للخيار الأول، 1 للخيار الثاني، إلخ).

أنتج JSON صالح فقط بدون أي نص إضافي أو شرح.
"""

        # استدعاء Gemini لإنشاء أسئلة الاختبار
        response = await gemini_api.generate_content_async(prompt)
        response_text = response.text

        # استخراج JSON من النص
        import json
        import re

        # البحث عن نمط JSON في النص
        json_match = re.search(r'\[[\s\S]*\]', response_text)
        if json_match:
            json_str = json_match.group(0)
            # تحليل JSON
            try:
                questions = json.loads(json_str)
                logger.info(f"Successfully generated {len(questions)} quiz questions for user {user_id}")
                return questions
            except json.JSONDecodeError as e:
                logger.error(f"Error parsing JSON from Gemini response: {e}")
                logger.error(f"JSON string: {json_str}")
                return []
        else:
            logger.error(f"Could not find JSON array in Gemini response")
            logger.error(f"Response: {response_text}")
            return []

    except Exception as e:
        logger.error(f"Error generating quiz questions for user {user_id}: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return []

async def send_quiz_question(update: Update, context: ContextTypes.DEFAULT_TYPE, user_id: str, lang: str, question_index: int):
    """إرسال سؤال الاختبار كاستطلاع رأي (poll)"""
    try:
        logger.info(f"Sending quiz question {question_index+1} to user {user_id}")
        state = user_education_state.get(user_id)

        if not state or not state.get('quiz_started') or not state.get('quiz_questions'):
            logger.warning(f"Quiz state not properly initialized for user {user_id}")
            return

        questions = state['quiz_questions']

        # التحقق من أن مؤشر السؤال ضمن النطاق
        if question_index >= len(questions):
            logger.warning(f"Question index {question_index} out of range for user {user_id}")
            # إنهاء الاختبار وعرض النتائج
            state['quiz_completed'] = True
            await show_quiz_results_or_next_steps(update, context, user_id, lang)
            return

        # الحصول على السؤال الحالي
        current_question = questions[question_index]
        question_text = current_question['question']
        options = current_question['options']
        correct_option_id = current_question['correct_option_id']

        # تحديث مؤشر السؤال الحالي
        state['current_question_index'] = question_index

        # إرسال السؤال كاستطلاع رأي من نوع اختبار
        from telegram import Poll

        # الحصول على chat_id بطريقة آمنة
        chat_id = None
        if update.callback_query and update.callback_query.message:
            chat_id = update.callback_query.message.chat_id
        elif update.message:
            chat_id = update.message.chat_id
        elif update.effective_chat:
            chat_id = update.effective_chat.id

        # إذا لم نتمكن من الحصول على chat_id من update، نحاول الحصول عليه من حالة المستخدم
        if chat_id is None and 'chat_id' in state:
            chat_id = state['chat_id']

        # إذا لم نتمكن من الحصول على chat_id، نسجل خطأ ونخرج
        if chat_id is None:
            logger.error(f"Could not determine chat_id for user {user_id}")
            return

        # تخزين chat_id في حالة المستخدم للاستخدام المستقبلي
        state['chat_id'] = chat_id

        # إضافة رقم السؤال إلى النص
        question_text_with_number = f"السؤال {question_index+1}/{len(questions)}: {question_text}"

        # إرسال السؤال كاستطلاع رأي من نوع اختبار
        message = await context.bot.send_poll(
            chat_id=chat_id,
            question=question_text_with_number,
            options=options,
            type=Poll.QUIZ,
            correct_option_id=correct_option_id,
            is_anonymous=False,
            explanation=None,  # يمكن إضافة شرح للإجابة الصحيحة هنا
            open_period=30,  # مدة الاستطلاع بالثواني
        )

        # حفظ معلومات الاستطلاع في حالة المستخدم
        poll_payload = {
            message.poll.id: {
                "question_index": question_index,
                "message_id": message.message_id,
                "chat_id": chat_id,
                "correct_option_id": correct_option_id
            }
        }

        # تخزين معلومات الاستطلاع في bot_data
        if 'quiz_polls' not in context.bot_data:
            context.bot_data['quiz_polls'] = {}

        context.bot_data['quiz_polls'].update(poll_payload)

        # تخزين معرف الاستطلاع في حالة المستخدم
        if 'active_polls' not in state:
            state['active_polls'] = []

        state['active_polls'].append(message.poll.id)

        logger.info(f"Sent quiz question {question_index+1}/{len(questions)} to user {user_id}")

    except Exception as e:
        logger.error(f"Error sending quiz question to user {user_id}: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())

        try:
            # محاولة إرسال رسالة خطأ للمستخدم
            chat_id = None
            if update.callback_query and update.callback_query.message:
                chat_id = update.callback_query.message.chat_id
            elif update.message:
                chat_id = update.message.chat_id
            elif update.effective_chat:
                chat_id = update.effective_chat.id
            elif user_id in user_education_state and 'chat_id' in user_education_state[user_id]:
                chat_id = user_education_state[user_id]['chat_id']

            if chat_id:
                await context.bot.send_message(
                    chat_id=chat_id,
                    text=get_text('quiz_question_error', lang, default="حدث خطأ أثناء إرسال السؤال. يرجى المحاولة مرة أخرى.")
                )
        except Exception as send_error:
            logger.error(f"Error sending error message to user {user_id}: {str(send_error)}")

async def handle_quiz_answer(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """معالجة إجابة المستخدم على سؤال الاختبار"""
    try:
        # الحصول على معلومات الإجابة
        answer = update.poll_answer
        poll_id = answer.poll_id
        selected_option = answer.option_ids[0] if answer.option_ids else None
        user_id = str(answer.user.id)

        logger.info(f"Received quiz answer from user {user_id} for poll {poll_id}, selected option: {selected_option}")

        # التحقق من وجود معلومات الاستطلاع في bot_data
        if 'quiz_polls' not in context.bot_data or poll_id not in context.bot_data['quiz_polls']:
            logger.warning(f"Poll {poll_id} not found in bot_data")
            return

        # الحصول على معلومات الاستطلاع
        poll_info = context.bot_data['quiz_polls'][poll_id]
        question_index = poll_info['question_index']
        correct_option_id = poll_info['correct_option_id']
        chat_id = poll_info['chat_id']

        # التحقق من وجود حالة المستخدم
        if user_id not in user_education_state:
            logger.warning(f"User {user_id} not found in user_education_state")
            return

        state = user_education_state[user_id]

        # تخزين إجابة المستخدم
        if 'user_answers' not in state:
            state['user_answers'] = []

        # التأكد من أن مصفوفة الإجابات كبيرة بما يكفي
        while len(state['user_answers']) <= question_index:
            state['user_answers'].append(None)

        state['user_answers'][question_index] = selected_option

        # التحقق مما إذا كانت الإجابة صحيحة
        is_correct = (selected_option == correct_option_id)

        # تحديث عدد الإجابات الصحيحة
        if is_correct:
            state['correct_answers'] = state.get('correct_answers', 0) + 1

        # الحصول على لغة المستخدم
        lang = await get_user_language(user_id)

        # انتظار فترة قصيرة للسماح للمستخدمين برؤية نتيجة السؤال
        import asyncio
        await asyncio.sleep(3)

        # إرسال السؤال التالي أو إنهاء الاختبار
        next_question_index = question_index + 1
        total_questions = len(state.get('quiz_questions', []))

        if next_question_index < total_questions:
            # إرسال السؤال التالي باستخدام chat_id بدلاً من message_target
            try:
                # إنشاء سؤال الاختبار التالي
                current_question = state['quiz_questions'][next_question_index]
                question_text = current_question['question']
                options = current_question['options']
                correct_option_id = current_question['correct_option_id']

                # تحديث مؤشر السؤال الحالي
                state['current_question_index'] = next_question_index

                # إضافة رقم السؤال إلى النص
                question_text_with_number = f"السؤال {next_question_index+1}/{total_questions}: {question_text}"

                # إرسال السؤال كاستطلاع رأي من نوع اختبار
                message = await context.bot.send_poll(
                    chat_id=chat_id,
                    question=question_text_with_number,
                    options=options,
                    type=Poll.QUIZ,
                    correct_option_id=correct_option_id,
                    is_anonymous=False,
                    explanation=None,
                    open_period=30,
                )

                # حفظ معلومات الاستطلاع في حالة المستخدم
                poll_payload = {
                    message.poll.id: {
                        "question_index": next_question_index,
                        "message_id": message.message_id,
                        "chat_id": chat_id,
                        "correct_option_id": correct_option_id
                    }
                }

                # تخزين معلومات الاستطلاع في bot_data
                if 'quiz_polls' not in context.bot_data:
                    context.bot_data['quiz_polls'] = {}

                context.bot_data['quiz_polls'].update(poll_payload)

                # تخزين معرف الاستطلاع في حالة المستخدم
                if 'active_polls' not in state:
                    state['active_polls'] = []

                state['active_polls'].append(message.poll.id)

                logger.info(f"Sent quiz question {next_question_index+1}/{total_questions} to user {user_id}")
            except Exception as e:
                logger.error(f"Error sending next quiz question to user {user_id}: {str(e)}")
                # إرسال رسالة خطأ للمستخدم
                await context.bot.send_message(
                    chat_id=chat_id,
                    text=get_text('quiz_question_error', lang, default="حدث خطأ أثناء إرسال السؤال التالي. يرجى المحاولة مرة أخرى.")
                )
        else:
            # إنهاء الاختبار وعرض النتائج
            state['quiz_completed'] = True

            # حساب النتيجة النهائية
            score = state.get('correct_answers', 0)
            state['quiz_score'] = score
            state['quiz_taken'] = True

            # عرض النتائج باستخدام chat_id بدلاً من message_target
            try:
                # حساب النسبة المئوية للإجابات الصحيحة
                percentage = (score / total_questions) * 100 if total_questions > 0 else 0

                # إنشاء رسالة النتائج
                result_message = (
                    f"**{get_text('quiz_results', lang, default='نتائج الاختبار')}** 📝\n\n"
                    f"**{get_text('correct_answers', lang, default='الإجابات الصحيحة')}**: {score}/{total_questions}\n"
                    f"**{get_text('percentage', lang, default='النسبة المئوية')}**: {percentage:.1f}%\n\n"
                )

                # إصلاح تنسيق النص العريض (إزالة النقطتين من داخل النص العريض)
                result_message = fix_bold_formatting(result_message)

                # إضافة تقييم بناءً على النسبة المئوية
                if percentage >= 90:
                    result_message += get_text('excellent_result', lang, default="🌟 ممتاز! لديك فهم ممتاز للمفاهيم.")
                elif percentage >= 70:
                    result_message += get_text('good_result', lang, default="👍 جيد! لديك فهم جيد للمفاهيم الأساسية.")
                elif percentage >= 50:
                    result_message += get_text('average_result', lang, default="🤔 متوسط. هناك بعض المفاهيم التي تحتاج إلى مراجعة.")
                else:
                    result_message += get_text('needs_improvement', lang, default="📚 تحتاج إلى مزيد من الدراسة. لا تقلق، استمر في التعلم!")

                # إرسال رسالة النتائج
                try:
                    await context.bot.send_message(
                        chat_id=chat_id,
                        text=result_message,
                        parse_mode=ParseMode.MARKDOWN
                    )
                except Exception as markdown_error:
                    logger.error(f"Error sending quiz results with Markdown: {str(markdown_error)}")
                    # محاولة إرسال النص بدون تنسيق Markdown
                    await context.bot.send_message(
                        chat_id=chat_id,
                        text=result_message.replace('**', '')
                    )

                # تحديد المواضيع التي تحتاج إلى مراجعة (إذا كانت النتيجة منخفضة)
                if percentage < 70 and 'user_answers' in state and 'quiz_questions' in state:
                    # تحديد الأسئلة التي أجاب عليها المستخدم بشكل خاطئ
                    incorrect_questions = []
                    user_answers = state.get('user_answers', [])
                    quiz_questions = state.get('quiz_questions', [])

                    for i, (answer, question) in enumerate(zip(user_answers, quiz_questions)):
                        if answer is None or answer != question.get('correct_option_id'):
                            incorrect_questions.append(question)

                    if incorrect_questions:
                        # استخراج المواضيع من الأسئلة الخاطئة
                        topics_to_review = [q.get('question', '').split(':')[0].strip() if ':' in q.get('question', '') else q.get('question', '') for q in incorrect_questions]

                        # إنشاء مواد مراجعة
                        review_material = await generate_review_material(user_id, lang, topics_to_review)

                        # إرسال مواد المراجعة
                        try:
                            await context.bot.send_message(
                                chat_id=chat_id,
                                text=f"**{get_text('review_material_title', lang, default='مواد للمراجعة')}** 📚\n\n{review_material}",
                                parse_mode=ParseMode.MARKDOWN
                            )
                        except Exception as markdown_error:
                            logger.error(f"Error sending review material with Markdown: {str(markdown_error)}")
                            # محاولة إرسال النص بدون تنسيق Markdown
                            await context.bot.send_message(
                                chat_id=chat_id,
                                text=f"{get_text('review_material_title', lang, default='مواد للمراجعة')} 📚\n\n{review_material}"
                            )

                # إضافة أزرار للخطوات التالية
                keyboard = []

                # إضافة زر للفصول التكميلية بناءً على نتيجة الاختبار
                keyboard.append([InlineKeyboardButton(
                    get_text('supplementary_chapters', lang, default="📚 فصول تكميلية مخصصة"),
                    callback_data='supplementary_chapters'
                )])

                # إضافة زر للعودة إلى القائمة الرئيسية
                keyboard.append([InlineKeyboardButton(
                    get_text('back_to_main', lang, default="🔙 العودة إلى القائمة الرئيسية"),
                    callback_data='back_to_main'
                )])

                await context.bot.send_message(
                    chat_id=chat_id,
                    text=get_text('next_steps', lang, default="ماذا تريد أن تفعل الآن؟"),
                    reply_markup=InlineKeyboardMarkup(keyboard)
                )
            except Exception as e:
                logger.error(f"Error showing quiz results to user {user_id}: {str(e)}")
                # إرسال رسالة خطأ للمستخدم
                await context.bot.send_message(
                    chat_id=chat_id,
                    text=get_text('results_error', lang, default="حدث خطأ أثناء عرض النتائج. يرجى المحاولة مرة أخرى.")
                )

    except Exception as e:
        logger.error(f"Error handling quiz answer: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())

async def generate_review_material(user_id: str, lang: str, topics: list):
    """إنشاء مواد مراجعة للمواضيع التي يحتاج المستخدم لمراجعتها"""
    try:
        logger.info(f"Generating review material for user {user_id} in {lang}")

        # الحصول على نموذج Gemini للمستخدم
        gemini_api = await get_gemini_api_for_user(user_id)
        if not gemini_api:
            logger.warning(f"Gemini API not available for user {user_id} for review generation")
            return get_text('gemini_access_error', lang)

        # بناء المطالبة لإنشاء مواد المراجعة
        topics_str = "\n".join([f"- {topic}" for topic in topics])

        logger.info(f"Topics for review for user {user_id}: {topics_str}")

        prompt = f"""
أنت مدرس خبير في التداول. المستخدم يحتاج إلى مراجعة المفاهيم التالية التي واجه صعوبة فيها خلال الاختبار:

{topics_str}

قم بإنشاء ملخص مراجعة موجز وواضح باللغة {lang} يغطي هذه المفاهيم. استخدم تنسيق نصي بسيط (بدون Markdown معقد) واستخدم الإيموجي المناسبة لجعل المحتوى أكثر جاذبية.

يجب أن يتضمن الملخص:
1. شرح مبسط للمفاهيم الأساسية
2. نقاط رئيسية يجب تذكرها
3. أمثلة توضيحية قصيرة
4. نصائح عملية للتطبيق

اجعل المحتوى موجزًا ومركزًا ومفيدًا للمراجعة السريعة.

ملاحظات مهمة للتنسيق:
- تجنب استخدام علامات Markdown المعقدة
- استخدم النجمة (*) للنقاط فقط
- تجنب استخدام الروابط
- تجنب استخدام الجداول
- تجنب استخدام الرموز الخاصة مثل _ و ~ و ` و | و > بكثرة
- تجنب استخدام علامات النجمة المزدوجة (**) للتنسيق
- استخدم الإيموجي بشكل معتدل
"""

        # استدعاء Gemini لإنشاء مواد المراجعة
        logger.info(f"Calling Gemini API to generate review material for user {user_id}")
        response = await gemini_api.generate_content_async(prompt)
        review_material = response.text

        logger.info(f"Successfully generated review material for user {user_id}, length: {len(review_material)} chars")

        # تنظيف المحتوى لتجنب مشاكل التنسيق
        # إصلاح تنسيق النص العريض (إزالة النقطتين من داخل النص العريض)
        review_material = fix_bold_formatting(review_material)

        return review_material

    except Exception as e:
        logger.error(f"Error generating review material for user {user_id}: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return get_text('review_generation_error', lang, default="حدث خطأ أثناء إنشاء مواد المراجعة. يرجى المحاولة مرة أخرى لاحقًا.")

async def handle_ask_ai_tutor_button(update: Update, context: ContextTypes.DEFAULT_TYPE, user_id: str, lang: str):
    """معالجة الضغط على زر 'اسأل الذكاء الاصطناعي'"""
    query = update.callback_query

    # تعيين حالة المستخدم إلى وضع طرح السؤال
    user_education_state[user_id]['is_asking_ai'] = True

    # إرسال رسالة توجيهية للمستخدم
    await query.message.reply_text(
        get_text('ask_tutor_instruction', lang, default="يرجى كتابة سؤالك المتعلق بالتداول وسأقوم بالإجابة عليه.")
    )

    logger.info(f"User {user_id} set to AI tutor question mode")


# --- Main Bot Setup (Example - needs integration into your main bot file) ---
# from telegram.ext import Application, CommandHandler, MessageHandler, filters, CallbackQueryHandler

# def setup_trading_education_handlers(application: Application):
#     application.add_handler(CommandHandler("learn_trading_ai", handle_learn_trading_ai))
#     application.add_handler(CallbackQueryHandler(button_handler))
#     # Add a message handler specifically for when the user is in the 'asking tutor' state
#     # This requires managing user states more robustly (e.g., using context.user_data)
#     application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND & filters.ChatType.PRIVATE, handle_message_for_ai_tutor)) # Simplified filter

async def generate_chapter_content(user_id: str, chapter_number: int, lang: str = 'ar'):
    """Generate content for a specific chapter using Gemini."""
    try:
        logger.info(f"Generating chapter {chapter_number} for user {user_id} in {lang}")
        gemini_api = await get_gemini_api_for_user(user_id)
        if not gemini_api:
            logger.warning(f"Gemini API not available for user {user_id} for chapter generation.")
            return _("لا يمكن إنشاء المحتوى التعليمي حاليًا. يرجى التأكد من إعداب مفتاح Gemini API الخاص بك.", lang)

        chapter_info = CHAPTERS.get(chapter_number)
        if not chapter_info:
            return _("الفصل المطلوب غير موجود.", lang)

        topic = chapter_info['topic'][lang]
        # تحسين المطالبة لتكون أكثر تحديدًا وتطلب تنسيق Markdown وإيموجيات ذات صلة
        prompt = (
            f"أنت خبير في تعليم التداول بالعملات الرقمية. اشرح الموضوع التالي للمبتدئين بلغة {lang}:\n\n"
            f"**الموضوع:** {topic}\n\n"
            f"**المطلوب:**\n\n"
            f"1.  **شرح مفصل وواضح:** استخدم لغة بسيطة ومباشرة.\n\n"
            f"2.  **أمثلة عملية:** قدم أمثلة لتوضيح المفاهيم.\n\n"
            f"3.  **تنسيق Markdown:** استخدم تنسيق Markdown لتنظيم النص (عناوين، قوائم نقطية، خط عريض، إلخ).\n\n"
            f"4.  **استخدام الإيموجيات بكثرة:** أضف إيموجيات مناسبة للمحتوى في كل فقرة وعنوان لجعله أكثر جاذبية وسهولة في القراءة. استخدم مجموعة متنوعة من الإيموجيات المناسبة للتداول مثل (📈, 📉, 📊, 💡, 💰, 🤔, 📱, 💹, 📋, 🔍, 🎯, ⚠️, ✅, ❌, 💼, 🔄, 📆, 🔔, 📢, 💪, 🧠, 🔑, 🛡️, 🚀, 💎, 🔮). ضع إيموجي مناسب قبل كل عنوان وفي بداية كل فقرة مهمة.\n\n"
            f"5.  **التركيز على المبتدئين:** تجنب المصطلحات المعقدة قدر الإمكان أو اشرحها ببساطة.\n\n"
            f"6.  **الرد باللغة المطلوبة فقط:** ({lang}).\n\n"
            f"**هام:** تأكد من أن الرد بأكمله بتنسيق Markdown صحيح وجاهز للعرض مباشرة. استخدم الإيموجيات بكثرة لجعل المحتوى أكثر جاذبية وتفاعلية."
        )

        response_text = await gemini_api.generate_text(prompt)

        # التحقق الأساسي من الاستجابة قبل محاولة التحليل
        if not response_text or not isinstance(response_text, str) or not response_text.strip():
            logger.error(f"Invalid or empty response from Gemini for chapter {chapter_number}, user {user_id}")
            return _("حدث خطأ أثناء إنشاء محتوى الفصل. حاول مرة أخرى لاحقًا.", lang)

        # إزالة أي بادئات أو لواحق غير متوقعة قد تسبب مشاكل في التحليل (مثل ```markdown)
        response_text = response_text.strip()
        if response_text.startswith("```markdown"):
            response_text = response_text[len("```markdown"):].strip()
        if response_text.endswith("```"):
            response_text = response_text[:-len("```")].strip()

        # -- لا يوجد تحليل JSON هنا، النص هو المحتوى مباشرة --

        # إصلاح تنسيق النص العريض (إزالة النقطتين من داخل النص العريض)
        response_text = fix_bold_formatting(response_text)

        logger.info(f"Successfully generated chapter {chapter_number} content for user {user_id}")
        return response_text

    except Exception as e:
        logger.error(f"Error generating chapter {chapter_number} content for user {user_id}: {str(e)}")
        import traceback
        logger.error(traceback.format_exc()) # تسجيل التتبع الكامل للخطأ
        return _("حدث خطأ غير متوقع أثناء إنشاء محتوى الفصل. يرجى المحاولة مرة أخرى أو التواصل مع الدعم.", lang)

async def send_chapter(update: Update, context: CallbackContext, chapter_number: int):
    """Send a specific chapter to the user."""
    query = update.callback_query
    user_id = str(query.from_user.id)
    lang = user_education_state.get(user_id, {}).get('lang', 'ar')

    await query.answer() # Acknowledge the button press

    # إظهار رسالة انتظار
    wait_message = await query.edit_message_text(
        text=f"⏳ {_('جاري تحميل الفصل', lang)} {chapter_number}...",
        reply_markup=None
    )

    content = await generate_chapter_content(user_id, chapter_number, lang)

    chapter_info = CHAPTERS.get(chapter_number)
    if not chapter_info:
        await wait_message.edit_text(_("الفصل المطلوب غير موجود.", lang))
        return

    # إزالة الإيموجي الثابت من العنوان
    title = f"**{_('الفصل', lang)} {chapter_number}: {chapter_info['topic'][lang]}**"

    # إصلاح تنسيق النص العريض (إزالة النقطتين من داخل النص العريض)
    content = fix_bold_formatting(content)

    # إضافة العنوان إلى المحتوى
    full_content = f"{title}\n\n{content}"

    keyboard = [
        # ... (أزرار التنقل الأخرى)
    ]

    # إضافة زر العودة إلى القائمة الرئيسية
    keyboard.append([InlineKeyboardButton(_("🔙 العودة إلى قائمة التعلم", lang), callback_data='learn_trading_ai')])

    reply_markup = InlineKeyboardMarkup(keyboard)

    try:
        await wait_message.edit_text(
            text=full_content,
            reply_markup=reply_markup,
            parse_mode=ParseMode.MARKDOWN,
            disable_web_page_preview=True
        )
        user_education_state[user_id]['current_chapter'] = chapter_number
    except telegram.error.BadRequest as e:
        logger.error(f"Error sending chapter {chapter_number} to user {user_id} (Markdown parse error?): {e}")
        # إرسال رسالة خطأ للمستخدم وإظهار المحتوى كنص عادي إذا فشل تحليل Markdown
        error_text = _("حدث خطأ في عرض الفصل. قد يكون هناك مشكلة في التنسيق. جارٍ عرض المحتوى كنص عادي:", lang)
        await wait_message.edit_text(f"{error_text}\n\n{title}\n\n{content}", reply_markup=reply_markup, disable_web_page_preview=True)
        user_education_state[user_id]['current_chapter'] = chapter_number
    except Exception as e:
        logger.error(f"Unexpected error sending chapter {chapter_number} to user {user_id}: {e}")
        await wait_message.edit_text(_("حدث خطأ غير متوقع أثناء عرض الفصل.", lang), reply_markup=reply_markup)

async def show_supplementary_chapters(update: Update, context: ContextTypes.DEFAULT_TYPE, user_id: str, lang: str):
    """عرض قائمة بالفصول التكميلية المتاحة بناءً على نتيجة الاختبار"""
    logger.info(f"Showing supplementary chapters for user {user_id}")

    # الحصول على حالة المستخدم
    state = user_education_state.get(user_id)
    if not state or not state.get('quiz_taken', False):
        # إذا لم يكن المستخدم قد أكمل الاختبار، نطلب منه إكمال الاختبار أولاً
        message_target = update.callback_query.message if update.callback_query else update.message
        await message_target.reply_text(get_text('complete_quiz_first', lang, default="يرجى إكمال الاختبار أولاً للحصول على فصول تكميلية مخصصة."))
        return

    # الحصول على نتيجة الاختبار
    quiz_score = state.get('quiz_score', 0)
    total_questions = state.get('total_questions', 20)  # تم تحديث عدد الأسئلة إلى 20
    percentage = (quiz_score / total_questions) * 100 if total_questions > 0 else 0

    logger.info(f"User {user_id} quiz score: {quiz_score}/{total_questions} ({percentage:.1f}%)")

    # تحديد الفصول التكميلية بناءً على نتيجة الاختبار
    supplementary_chapters = await determine_supplementary_chapters(user_id, lang, percentage)

    if not supplementary_chapters:
        message_target = update.callback_query.message if update.callback_query else update.message
        await message_target.reply_text(get_text('no_supplementary_chapters', lang, default="لا توجد فصول تكميلية متاحة حاليًا. يرجى المحاولة مرة أخرى لاحقًا."))
        return

    # إنشاء أزرار للفصول التكميلية
    keyboard = []

    # تسجيل عدد الفصول التكميلية المتاحة
    logger.info(f"Found {len(supplementary_chapters)} supplementary chapters for user {user_id}")

    # إضافة أزرار للفصول التكميلية
    for chapter_id, chapter_info in supplementary_chapters.items():
        chapter_title = chapter_info['title']
        logger.info(f"Adding supplementary chapter button: {chapter_id} - {chapter_title}")
        keyboard.append([InlineKeyboardButton(
            f"📘 {chapter_title}",
            callback_data=f"supplementary_chapter_{chapter_id}"
        )])

    # إضافة زر للعودة
    keyboard.append([InlineKeyboardButton(
        get_text('back_button', lang, default="🔙 رجوع"),
        callback_data="back_to_quiz_results"
    )])

    reply_markup = InlineKeyboardMarkup(keyboard)

    # إرسال رسالة مع قائمة الفصول التكميلية
    message_target = update.callback_query.message if update.callback_query else update.message

    try:
        await message_target.reply_text(
            get_text('supplementary_chapters_list', lang, default="📚 الفصول التكميلية المخصصة لك بناءً على نتيجة الاختبار:"),
            reply_markup=reply_markup
        )
        logger.info(f"Successfully sent supplementary chapters list to user {user_id}")
    except Exception as e:
        logger.error(f"Error sending supplementary chapters list to user {user_id}: {str(e)}")
        # محاولة إرسال رسالة جديدة بدلاً من الرد
        try:
            chat_id = message_target.chat_id
            await context.bot.send_message(
                chat_id=chat_id,
                text=get_text('supplementary_chapters_list', lang, default="📚 الفصول التكميلية المخصصة لك بناءً على نتيجة الاختبار:"),
                reply_markup=reply_markup
            )
            logger.info(f"Successfully sent supplementary chapters list as new message to user {user_id}")
        except Exception as e2:
            logger.error(f"Error sending supplementary chapters as new message to user {user_id}: {str(e2)}")

async def determine_supplementary_chapters(user_id: str, lang: str, percentage: float):
    """تحديد الفصول التكميلية المناسبة بناءً على نتيجة الاختبار"""
    # الحصول على حالة المستخدم
    state = user_education_state.get(user_id, {})

    # تسجيل معلومات عن حالة المستخدم
    logger.info(f"Determining supplementary chapters for user {user_id} with score percentage: {percentage:.1f}%")

    # تحديد المواضيع التي يحتاج المستخدم لمراجعتها
    topics_to_review = []

    # إذا كان لدينا معلومات عن الإجابات الخاطئة
    if 'user_answers' in state and 'quiz_questions' in state:
        user_answers = state.get('user_answers', [])
        quiz_questions = state.get('quiz_questions', [])

        logger.info(f"User {user_id} has {len(user_answers)} answers and {len(quiz_questions)} questions")

        # تحديد الأسئلة التي أجاب عليها المستخدم بشكل خاطئ
        for i, (answer, question) in enumerate(zip(user_answers, quiz_questions)):
            if answer is None or answer != question.get('correct_option_id'):
                # استخراج موضوع السؤال
                question_text = question.get('question', '')
                topic = question_text.split(':')[0].strip() if ':' in question_text else question_text
                topics_to_review.append(topic)
                logger.info(f"User {user_id} needs to review topic: {topic}")

    # إنشاء قائمة بالفصول التكميلية
    supplementary_chapters = {}

    # إضافة فصول تكميلية بناءً على مستوى الأداء
    if percentage < 50:
        # للمستخدمين ذوي الأداء المنخفض، نقدم فصولاً أساسية
        supplementary_chapters["basics"] = {
            "title": get_text('basics_chapter_title', lang, default="أساسيات التداول - مراجعة شاملة"),
            "description": get_text('basics_chapter_desc', lang, default="مراجعة شاملة للمفاهيم الأساسية في التداول"),
            "level": "beginner"
        }
        supplementary_chapters["risk_management"] = {
            "title": get_text('risk_management_title', lang, default="إدارة المخاطر للمبتدئين"),
            "description": get_text('risk_management_desc', lang, default="تعلم كيفية حماية رأس مالك وتقليل الخسائر"),
            "level": "beginner"
        }
        logger.info(f"Added beginner chapters for user {user_id} due to low score")
    elif percentage < 70:
        # للمستخدمين ذوي الأداء المتوسط، نقدم فصولاً متوسطة
        supplementary_chapters["intermediate_strategies"] = {
            "title": get_text('intermediate_strategies_title', lang, default="استراتيجيات التداول المتوسطة"),
            "description": get_text('intermediate_strategies_desc', lang, default="استراتيجيات متقدمة للمتداولين ذوي الخبرة المتوسطة"),
            "level": "intermediate"
        }
        supplementary_chapters["technical_analysis_deep"] = {
            "title": get_text('technical_analysis_title', lang, default="التحليل الفني المتقدم"),
            "description": get_text('technical_analysis_desc', lang, default="فهم متعمق للمؤشرات والأنماط الفنية"),
            "level": "intermediate"
        }
        logger.info(f"Added intermediate chapters for user {user_id} due to medium score")
    else:
        # للمستخدمين ذوي الأداء العالي، نقدم فصولاً متقدمة
        supplementary_chapters["advanced_strategies"] = {
            "title": get_text('advanced_strategies_title', lang, default="استراتيجيات التداول المتقدمة"),
            "description": get_text('advanced_strategies_desc', lang, default="استراتيجيات متقدمة للمتداولين المحترفين"),
            "level": "advanced"
        }
        supplementary_chapters["market_psychology"] = {
            "title": get_text('market_psychology_title', lang, default="علم نفس السوق والتداول العاطفي"),
            "description": get_text('market_psychology_desc', lang, default="فهم العوامل النفسية التي تؤثر على قرارات التداول"),
            "level": "advanced"
        }
        logger.info(f"Added advanced chapters for user {user_id} due to high score")

    # إضافة فصول تكميلية بناءً على المواضيع التي يحتاج المستخدم لمراجعتها
    for topic in topics_to_review:
        topic_lower = topic.lower() if isinstance(topic, str) else ""

        # تحديد الفصل التكميلي المناسب بناءً على الموضوع
        if any(keyword in topic_lower for keyword in ["مؤشر", "تحليل فني", "مؤشرات", "indicator", "technical"]):
            supplementary_chapters["indicators_deep_dive"] = {
                "title": get_text('indicators_title', lang, default="تعمق في المؤشرات الفنية"),
                "description": get_text('indicators_desc', lang, default="شرح مفصل للمؤشرات الفنية وكيفية استخدامها"),
                "level": "intermediate",
                "topic": "technical_analysis"
            }
            logger.info(f"Added technical analysis chapter for user {user_id} based on topic: {topic}")
        elif any(keyword in topic_lower for keyword in ["نفسي", "عاطف", "سلوك", "psychology", "emotion"]):
            supplementary_chapters["trading_psychology"] = {
                "title": get_text('psychology_title', lang, default="علم النفس في التداول"),
                "description": get_text('psychology_desc', lang, default="فهم العوامل النفسية وتأثيرها على قرارات التداول"),
                "level": "intermediate",
                "topic": "psychology"
            }
            logger.info(f"Added psychology chapter for user {user_id} based on topic: {topic}")
        elif any(keyword in topic_lower for keyword in ["استراتيجي", "خطة", "strategy", "plan"]):
            supplementary_chapters["trading_strategies_deep"] = {
                "title": get_text('strategies_title', lang, default="استراتيجيات التداول المتقدمة"),
                "description": get_text('strategies_desc', lang, default="استراتيجيات متقدمة للتداول في مختلف ظروف السوق"),
                "level": "advanced",
                "topic": "strategies"
            }
            logger.info(f"Added strategies chapter for user {user_id} based on topic: {topic}")
        elif any(keyword in topic_lower for keyword in ["مخاطر", "خسار", "إدارة", "risk", "loss", "management"]):
            supplementary_chapters["advanced_risk_management"] = {
                "title": get_text('adv_risk_title', lang, default="إدارة المخاطر المتقدمة"),
                "description": get_text('adv_risk_desc', lang, default="تقنيات متقدمة لإدارة المخاطر وحماية رأس المال"),
                "level": "intermediate",
                "topic": "risk_management"
            }
            logger.info(f"Added risk management chapter for user {user_id} based on topic: {topic}")

    # إذا لم يتم العثور على أي فصول تكميلية، نضيف فصلاً افتراضياً
    if not supplementary_chapters:
        supplementary_chapters["general_trading"] = {
            "title": get_text('general_trading_title', lang, default="مفاهيم التداول الأساسية"),
            "description": get_text('general_trading_desc', lang, default="مراجعة شاملة لمفاهيم التداول الأساسية"),
            "level": "beginner",
            "topic": "general"
        }
        logger.info(f"Added default chapter for user {user_id} as no specific chapters were determined")

    logger.info(f"Determined {len(supplementary_chapters)} supplementary chapters for user {user_id}")
    return supplementary_chapters

async def generate_and_send_supplementary_chapter(update: Update, context: ContextTypes.DEFAULT_TYPE, user_id: str, chapter_id: str, lang: str):
    """إنشاء وإرسال محتوى الفصل التكميلي المحدد"""
    logger.info(f"Generating supplementary chapter {chapter_id} for user {user_id}")

    # الحصول على حالة المستخدم
    state = user_education_state.get(user_id)
    if not state:
        logger.warning(f"User state not found for {user_id}")
        return

    # تحديد الفصول التكميلية المتاحة
    quiz_score = state.get('quiz_score', 0)
    total_questions = state.get('total_questions', 20)  # تم تحديث عدد الأسئلة إلى 20
    percentage = (quiz_score / total_questions) * 100 if total_questions > 0 else 0

    logger.info(f"Generating supplementary chapter for user {user_id} with score: {quiz_score}/{total_questions} ({percentage:.1f}%)")

    supplementary_chapters = await determine_supplementary_chapters(user_id, lang, percentage)

    # التحقق من وجود الفصل المطلوب
    if chapter_id not in supplementary_chapters:
        logger.warning(f"Chapter {chapter_id} not found for user {user_id}")
        message_target = update.callback_query.message if update.callback_query else update.message
        await message_target.reply_text(get_text('chapter_not_found', lang, default="الفصل المطلوب غير موجود."))
        return

    chapter_info = supplementary_chapters[chapter_id]
    logger.info(f"Found chapter {chapter_id}: {chapter_info['title']} for user {user_id}")

    # إظهار رسالة انتظار
    message_target = update.callback_query.message if update.callback_query else update.message

    try:
        wait_message = await message_target.reply_text(get_text('generating_chapter', lang, default="⏳ جاري إنشاء الفصل التكميلي..."))
        logger.info(f"Sent waiting message for chapter generation to user {user_id}")

        # الحصول على نموذج Gemini للمستخدم
        gemini_api = await get_gemini_api_for_user(user_id)
        if not gemini_api:
            logger.error(f"Gemini API not available for user {user_id}")
            await wait_message.edit_text(get_text('gemini_access_error', lang, default="لا يمكن الوصول إلى Gemini API. يرجى التحقق من مفتاح API الخاص بك."))
            return

        # بناء المطالبة لإنشاء محتوى الفصل التكميلي
        prompt = f"""
أنت مدرس خبير في التداول ومؤلف محتوى تعليمي. مهمتك هي إنشاء فصل تكميلي مخصص للمتعلم باللغة {lang}.

**معلومات الفصل:**
- العنوان: {chapter_info['title']}
- الوصف: {chapter_info['description']}
- المستوى: {chapter_info['level']}
- الموضوع: {chapter_info.get('topic', 'general')}

**تعليمات المحتوى:**
1. قم بإنشاء محتوى تعليمي موجز وفعال حول الموضوع المحدد.
2. استخدم لغة واضحة ومباشرة تناسب مستوى المتعلم ({chapter_info['level']}).
3. قسّم المحتوى إلى أقسام منطقية مع عناوين فرعية.
4. أضف أمثلة عملية موجزة لتوضيح المفاهيم.
5. استخدم الإيموجي المناسبة لجعل المحتوى أكثر جاذبية.
6. اختم بملخص قصير للنقاط الرئيسية.

**تنسيق المحتوى:**
- استخدم تنسيق نصي بسيط (تجنب Markdown المعقد).
- استخدم الإيموجي بشكل مناسب في بداية كل قسم.
- تجنب استخدام الرموز الخاصة مثل _ و ~ و ` و | و > بكثرة.
- تجنب استخدام الروابط والجداول.
- تجنب استخدام علامات النجمة المزدوجة (**) للتنسيق.
- يجب أن يكون المحتوى موجزًا ولا يتجاوز 3000 حرف.

أنشئ محتوى تعليميًا موجزًا وفعالًا يساعد المتعلم على فهم الموضوع بشكل أفضل.
"""

        logger.info(f"Calling Gemini API to generate chapter content for user {user_id}")
        # استدعاء Gemini لإنشاء محتوى الفصل
        response = await gemini_api.generate_content_async(prompt)
        chapter_content = response.text
        logger.info(f"Received chapter content from Gemini API for user {user_id}, length: {len(chapter_content)} chars")

        # إصلاح تنسيق النص العريض (إزالة النقطتين من داخل النص العريض)
        chapter_content = fix_bold_formatting(chapter_content)

        # إرسال محتوى الفصل
        try:
            # إضافة عنوان الفصل إلى المحتوى (بدون تنسيق Markdown)
            title = f"📘 {chapter_info['title']}"

            # تقسيم المحتوى إلى أجزاء إذا كان طويلاً جدًا
            MAX_MESSAGE_LENGTH = 3800  # الحد الأقصى لطول رسالة تلغرام (مع هامش أمان)

            # إنشاء أزرار التنقل
            keyboard = [
                [InlineKeyboardButton(
                    get_text('back_to_chapters', lang, default="🔙 العودة إلى قائمة الفصول التكميلية"),
                    callback_data="supplementary_chapters"
                )],
                [InlineKeyboardButton(
                    get_text('back_to_main', lang, default="🏠 العودة إلى القائمة الرئيسية"),
                    callback_data="back_to_main"
                )]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            # التحقق من طول المحتوى
            if len(title) + len(chapter_content) <= MAX_MESSAGE_LENGTH:
                # إذا كان المحتوى قصيرًا بما يكفي، نرسله كرسالة واحدة
                full_content = f"{title}\n\n{chapter_content}"

                # إرسال المحتوى بدون تنسيق Markdown لتجنب الأخطاء
                await wait_message.edit_text(
                    text=full_content,
                    reply_markup=reply_markup
                )
                logger.info(f"Successfully sent chapter content to user {user_id}")
            else:
                # إذا كان المحتوى طويلاً، نقسمه إلى أجزاء
                logger.info(f"Content too long ({len(chapter_content)} chars), splitting into parts")

                # إرسال العنوان في الرسالة الأولى
                await wait_message.edit_text(
                    text=f"{title}\n\n{get_text('content_too_long', lang, default='المحتوى طويل وسيتم إرساله في عدة رسائل:')}"
                )

                # تقسيم المحتوى إلى أجزاء
                parts = []
                current_part = ""

                # تقسيم المحتوى حسب الفقرات
                paragraphs = chapter_content.split('\n\n')

                for paragraph in paragraphs:
                    if len(current_part) + len(paragraph) + 2 <= MAX_MESSAGE_LENGTH:
                        if current_part:
                            current_part += "\n\n" + paragraph
                        else:
                            current_part = paragraph
                    else:
                        if current_part:
                            parts.append(current_part)
                        current_part = paragraph

                if current_part:
                    parts.append(current_part)

                # إرسال كل جزء كرسالة منفصلة
                for i, part in enumerate(parts):
                    part_header = f"الجزء {i+1}/{len(parts)}:\n\n" if len(parts) > 1 else ""

                    # إضافة أزرار التنقل فقط للجزء الأخير
                    if i == len(parts) - 1:
                        await context.bot.send_message(
                            chat_id=message_target.chat_id,
                            text=f"{part_header}{part}",
                            reply_markup=reply_markup
                        )
                    else:
                        await context.bot.send_message(
                            chat_id=message_target.chat_id,
                            text=f"{part_header}{part}"
                        )

                logger.info(f"Successfully sent chapter content in {len(parts)} parts to user {user_id}")

        except Exception as edit_error:
            logger.error(f"Error editing waiting message for user {user_id}: {str(edit_error)}")
            # محاولة إرسال رسالة جديدة بدلاً من تعديل الرسالة الحالية
            try:
                chat_id = message_target.chat_id
                await context.bot.send_message(
                    chat_id=chat_id,
                    text=get_text('chapter_generation_error', lang, default="حدث خطأ أثناء إرسال الفصل التكميلي. يرجى المحاولة مرة أخرى لاحقًا.")
                )
            except Exception as send_error:
                logger.error(f"Error sending error message to user {user_id}: {str(send_error)}")
                await message_target.reply_text(get_text('chapter_generation_error', lang, default="حدث خطأ أثناء إرسال الفصل التكميلي. يرجى المحاولة مرة أخرى لاحقًا."))

    except Exception as e:
        logger.error(f"Error generating supplementary chapter for user {user_id}: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())

        # محاولة إرسال رسالة خطأ للمستخدم
        try:
            await message_target.reply_text(get_text('chapter_generation_error', lang, default="حدث خطأ أثناء إنشاء الفصل التكميلي. يرجى المحاولة مرة أخرى لاحقًا."))
        except Exception as reply_error:
            logger.error(f"Error sending error message to user {user_id}: {str(reply_error)}")
            # محاولة إرسال رسالة جديدة
            try:
                chat_id = message_target.chat_id
                await context.bot.send_message(
                    chat_id=chat_id,
                    text=get_text('chapter_generation_error', lang, default="حدث خطأ أثناء إنشاء الفصل التكميلي. يرجى المحاولة مرة أخرى لاحقًا.")
                )
            except Exception as send_error:
                logger.error(f"Error sending error message as new message to user {user_id}: {str(send_error)}")

async def handle_ai_conversation(update: Update, context: CallbackContext):
    """Handle conversation with the AI assistant during education."""
    user_id = str(update.effective_user.id)
    lang = user_education_state.get(user_id, {}).get('lang', 'ar')
    user_message = update.message.text

    if user_id not in user_education_state or not user_education_state[user_id].get('is_asking_ai'):
        # تجاهل الرسالة إذا لم يكن المستخدم في وضع سؤال الذكاء الاصطناعي
        return

    # إظهار علامة "يكتب..."
    await context.bot.send_chat_action(chat_id=update.effective_chat.id, action=ChatAction.TYPING)

    try:
        gemini_api = await get_gemini_api_for_user(user_id)
        if not gemini_api:
            await update.message.reply_text(_("عذرًا، لا يمكنني الرد الآن. يرجى التأكد من إعداب مفتاح Gemini API.", lang))
            return

        # بناء سجل المحادثة (يمكن تحسينه لتضمين سياق الفصل الحالي)
        current_chapter = user_education_state[user_id].get('current_chapter', 1)
        chapter_topic = CHAPTERS.get(current_chapter, {}).get('topic', {}).get(lang, 'موضوع غير محدد')

        # استخدام سجل محادثة بسيط حاليًا
        history = user_education_state[user_id].setdefault('ai_conversation_history', [])

        # إضافة رسالة المستخدم إلى السجل
        history.append({"role": "user", "parts": [user_message]})

        # تحديد المطالبة الأولية مع السياق
        system_instruction = (
            f"أنت مساعد تعليمي متخصص في تداول العملات الرقمية. المستخدم يدرس حاليًا الفصل المتعلق بـ '{chapter_topic}'. "
            f"أجب على أسئلة المستخدم المتعلقة بهذا الموضوع أو بمواضيع التداول بشكل عام بلغة {lang}. "
            f"حافظ على إجاباتك واضحة وموجزة ومناسبة للمبتدئين. استخدم تنسيق Markdown عند الحاجة."
        )

        # التأكد من أن السجل لا يتجاوز حدًا معينًا (اختياري)
        MAX_HISTORY_TURNS = 5 # الاحتفاظ بآخر 5 تبادلات
        if len(history) > MAX_HISTORY_TURNS * 2:
             history = history[-(MAX_HISTORY_TURNS * 2):]

        # إنشاء كائن محادثة جديد في كل مرة أو استخدام كائن مستمر إذا كان النموذج يدعم ذلك
        # هنا نفترض إنشاء كائن جديد مع السجل في كل مرة
        chat_session = gemini_api.model.start_chat(history=history)

        # إرسال الرسالة إلى Gemini (مع إضافة التعليمات النظامية إذا لم تكن جزءًا من السجل)
        # ملاحظة: بعض واجهات برمجة التطبيقات قد تدعم instruction مباشرة
        # هنا ندمجها مع رسالة المستخدم إذا لزم الأمر أو نعتمد على السجل

        # الطريقة الأبسط: إرسال الرسالة الأخيرة مباشرة
        response = await chat_session.send_message_async(user_message) # قد تحتاج لتمرير السجل كاملاً حسب الـ SDK

        ai_response = response.text

        # إصلاح تنسيق النص العريض (إزالة النقطتين من داخل النص العريض)
        ai_response = fix_bold_formatting(ai_response)

        # إضافة رد الذكاء الاصطناعي إلى السجل
        history.append({"role": "model", "parts": [ai_response]})

        # إرسال الرد للمستخدم
        await update.message.reply_text(ai_response, parse_mode=ParseMode.MARKDOWN)

    except Exception as e:
        logger.error(f"Error in AI conversation for user {user_id}: {str(e)}")
        await update.message.reply_text(_("عذرًا، حدث خطأ أثناء معالجة طلبك. حاول مرة أخرى.", lang))
    finally:
        # إعادة المستخدم إلى وضع التعلم العادي بعد الرد
        user_education_state[user_id]['is_asking_ai'] = False

        # إنشاء أزرار التنقل
        keyboard = []

        # إضافة زر الفصل التالي إذا كان المستخدم في فصل أقل من 10
        current_chapter = user_education_state[user_id].get('current_chapter', 1)
        if current_chapter < 10:
            keyboard.append([InlineKeyboardButton(get_text('next_chapter_button', lang, default="الفصل التالي ❯"),
                                                callback_data=f'next_chapter_{current_chapter + 1}')])

        # إضافة زر لطرح سؤال آخر
        keyboard.append([InlineKeyboardButton(get_text('ask_tutor_button', lang, default="❓ اسأل مدرس الذكاء الاصطناعي"),
                                            callback_data='ask_ai_tutor')])

        # إرسال رسالة توضيحية للعودة إلى التعلم أو طرح سؤال آخر
        await context.bot.send_message(
            chat_id=update.effective_chat.id,
            text=_("continue_or_ask", lang, default="يمكنك الآن المتابعة في الفصل أو طرح سؤال آخر."),
            reply_markup=InlineKeyboardMarkup(keyboard)
        )
