import logging
import json
import aiohttp
import base64
import pytz
from datetime import datetime, timedelta
from urllib.parse import urlencode

# Configure logging
logging.basicConfig(format='%(asctime)s - %(name)s - %(levelname)s - %(message)s', level=logging.INFO)
logger = logging.getLogger(__name__)

class PayPalAPI:
    """واجهة برمجة تطبيقات PayPal"""

    def __init__(self, client_id, client_secret, is_sandbox=False):
        """تهيئة واجهة برمجة تطبيقات PayPal"""
        self.client_id = client_id
        self.client_secret = client_secret
        self.is_sandbox = is_sandbox

        # تحديد عنوان URL الأساسي بناءً على وضع الاختبار
        if is_sandbox:
            self.base_url = "https://api-m.sandbox.paypal.com"
        else:
            self.base_url = "https://api-m.paypal.com"

        self.access_token = None
        self.token_expiry = None

    async def get_access_token(self):
        """الحصول على رمز الوصول من PayPal"""
        # التحقق من وجود رمز وصول صالح
        if self.access_token and self.token_expiry and datetime.now() < self.token_expiry:
            return self.access_token

        try:
            # إنشاء بيانات الاعتماد
            credentials = f"{self.client_id}:{self.client_secret}"
            encoded_credentials = base64.b64encode(credentials.encode()).decode()

            # إعداد الرأس والبيانات
            headers = {
                "Authorization": f"Basic {encoded_credentials}",
                "Content-Type": "application/x-www-form-urlencoded"
            }
            data = {"grant_type": "client_credentials"}

            # إرسال الطلب
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/v1/oauth2/token",
                    headers=headers,
                    data=data
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        self.access_token = result.get("access_token")
                        # تعيين وقت انتهاء الصلاحية (عادة 3600 ثانية)
                        expires_in = result.get("expires_in", 3600)
                        self.token_expiry = datetime.now() + timedelta(seconds=expires_in - 60)  # تقليل دقيقة واحدة للأمان
                        return self.access_token
                    else:
                        logger.error(f"خطأ في الحصول على رمز الوصول: {response.status} - راجع استجابة PayPal لمزيد من التفاصيل.")
                        return None
        except Exception as e:
            logger.error(f"خطأ في الحصول على رمز الوصول: {str(e)}")
            return None

    async def verify_payment(self, order_id):
        """التحقق من حالة الدفع"""
        try:
            # الحصول على رمز الوصول
            access_token = await self.get_access_token()
            if not access_token:
                return None

            # إعداد الرأس
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            }

            # إرسال الطلب
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.base_url}/v2/checkout/orders/{order_id}",
                    headers=headers
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        return result
                    else:
                        logger.error(f"خطأ في التحقق من الدفع: {response.status} - راجع استجابة PayPal لمزيد من التفاصيل.")
                        return None
        except Exception as e:
            logger.error(f"خطأ في التحقق من الدفع: {str(e)}")
            return None

    async def create_order(self, amount, currency="USD", return_url=None, cancel_url=None, user_id=None, description=None, allow_guest_checkout=True):
        """إنشاء طلب دفع جديد

        Args:
            amount: المبلغ المطلوب دفعه
            currency: العملة (الافتراضية: USD)
            return_url: عنوان URL للعودة بعد نجاح الدفع
            cancel_url: عنوان URL للعودة بعد إلغاء الدفع
            user_id: معرف المستخدم
            description: وصف الطلب
            allow_guest_checkout: السماح بالدفع كضيف (بدون حساب PayPal)
        """
        try:
            # الحصول على رمز الوصول
            access_token = await self.get_access_token()
            if not access_token:
                return None

            # إعداد الرأس
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            }

            # إعداد وصف الطلب
            if not description:
                description = "اشتراك أسبوعي في بوت التحليل الفني" if not user_id else f"اشتراك أسبوعي في بوت التحليل الفني - المستخدم: {user_id}"

            # إعداد بيانات الطلب
            payload = {
                "intent": "CAPTURE",
                "purchase_units": [
                    {
                        "amount": {
                            "currency_code": currency,
                            "value": str(amount),
                            "breakdown": {
                                "item_total": {
                                    "currency_code": currency,
                                    "value": str(amount)
                                }
                            }
                        },
                        "description": description,
                        "items": [
                            {
                                "name": "اشتراك أسبوعي",
                                "description": "اشتراك أسبوعي في بوت التحليل الفني",
                                "quantity": "1",
                                "unit_amount": {
                                    "currency_code": currency,
                                    "value": str(amount)
                                },
                                "category": "DIGITAL_GOODS"
                            }
                        ]
                    }
                ],
                "application_context": {
                    "brand_name": "بوت التحليل الفني",
                    "locale": "ar-SA",
                    "shipping_preference": "NO_SHIPPING",
                    "user_action": "PAY_NOW",
                    "landing_page": "BILLING" if allow_guest_checkout else "LOGIN", # تعيين صفحة الهبوط إلى صفحة الفواتير لتسهيل الدفع كضيف
                    "payment_method": {
                        "payer_selected": "PAYPAL",
                        "payee_preferred": "UNRESTRICTED" if allow_guest_checkout else "IMMEDIATE_PAYMENT_REQUIRED" # السماح بالدفع كضيف
                    }
                },
                # نستخدم الإعدادات الأساسية فقط لضمان التوافق مع PayPal API
                # سنضيف دعم طرق الدفع البديلة في تحديث لاحق بعد التأكد من التوافق
            }

            # إضافة عناوين URL للإرجاع والإلغاء إذا تم توفيرها
            if return_url:
                payload["application_context"]["return_url"] = return_url
            if cancel_url:
                payload["application_context"]["cancel_url"] = cancel_url

            # تحسين تجربة الدفع على الأجهزة المحمولة
            # نستخدم الإعدادات المدعومة فقط من PayPal API
            # لا نستخدم user_experience لأنه غير مدعوم في الإصدار الحالي من API

            # إرسال الطلب
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/v2/checkout/orders",
                    headers=headers,
                    json=payload
                ) as response:
                    if response.status == 201:
                        result = await response.json()
                        return result
                    else:
                        logger.error(f"خطأ في إنشاء طلب دفع: {response.status} - راجع استجابة PayPal لمزيد من التفاصيل.")
                        return None
        except Exception as e:
            logger.error(f"خطأ في إنشاء طلب دفع: {str(e)}")
            return None

    async def capture_payment(self, order_id):
        """تأكيد الدفع"""
        try:
            # الحصول على رمز الوصول
            access_token = await self.get_access_token()
            if not access_token:
                return None

            # إعداد الرأس
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            }

            # إرسال الطلب
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/v2/checkout/orders/{order_id}/capture",
                    headers=headers
                ) as response:
                    if response.status == 201:
                        result = await response.json()
                        return result
                    else:
                        logger.error(f"خطأ في تأكيد الدفع: {response.status} - راجع استجابة PayPal لمزيد من التفاصيل.")
                        return None
        except Exception as e:
            logger.error(f"خطأ في تأكيد الدفع: {str(e)}")
            return None

    async def get_payment_details(self, payment_id):
        """الحصول على تفاصيل الدفع"""
        try:
            # الحصول على رمز الوصول
            access_token = await self.get_access_token()
            if not access_token:
                return None

            # إعداد الرأس
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            }

            # إرسال الطلب
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.base_url}/v2/payments/captures/{payment_id}",
                    headers=headers
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        return result
                    else:
                        logger.error(f"خطأ في الحصول على تفاصيل الدفع: {response.status} - راجع استجابة PayPal لمزيد من التفاصيل.")
                        return None
        except Exception as e:
            logger.error(f"خطأ في الحصول على تفاصيل الدفع: {str(e)}")
            return None

async def verify_paypal_transaction(user_id, amount=5.0, transaction_id=None):
    """التحقق من صحة معاملة PayPal للمستخدم"""
    try:
        # استيراد المكتبات اللازمة
        from firebase_admin import firestore
        from services.system_settings import system_settings

        # الحصول على قاعدة البيانات
        db = firestore.client()

        # الحصول على بيانات اعتماد PayPal من الإعدادات
        client_id = system_settings.get("PAYPAL_CLIENT_ID", sensitive=True)
        client_secret = system_settings.get("PAYPAL_CLIENT_SECRET", sensitive=True)
        is_sandbox = system_settings.get("PAYPAL_SANDBOX_MODE", False)

        if not client_id or not client_secret:
            logger.error("بيانات اعتماد PayPal غير متوفرة")
            return False

        # إنشاء كائن PayPalAPI
        paypal_api = PayPalAPI(client_id, client_secret, is_sandbox)

        # التحقق من المعاملة
        if transaction_id:
            # الحصول على المعاملة من قاعدة البيانات
            transaction_ref = db.collection('transactions').document(transaction_id)
            transaction_doc = transaction_ref.get()

            if not transaction_doc.exists:
                logger.warning(f"المعاملة {transaction_id} غير موجودة")
                return False

            transaction_data = transaction_doc.to_dict()

            # التحقق من أن المعاملة تخص المستخدم
            if transaction_data.get('user_id') != user_id:
                logger.warning(f"المعاملة {transaction_id} لا تخص المستخدم {user_id}")
                return False

            # الحصول على معرف الطلب من المعاملة
            order_id = transaction_data.get('order_id')
            if not order_id:
                logger.warning(f"لا يوجد معرف طلب للمعاملة {transaction_id}")
                return False

            # الحصول على تفاصيل الطلب
            order_details = await paypal_api.verify_payment(order_id)

            if not order_details:
                logger.warning(f"لم يتم العثور على تفاصيل الطلب {order_id}")
                return False

            # التحقق من حالة الطلب
            status = order_details.get("status")
            if status not in ["COMPLETED", "APPROVED"]:
                logger.warning(f"حالة الطلب {order_id} هي {status} وليست COMPLETED أو APPROVED")
                return False

            # التحقق من المبلغ
            purchase_units = order_details.get("purchase_units", [])
            if not purchase_units:
                logger.warning(f"لا توجد وحدات شراء في الطلب {order_id}")
                return False

            # التحقق من المبلغ
            order_amount = float(purchase_units[0].get("amount", {}).get("value", 0))
            if abs(order_amount - amount) > 0.01:
                logger.warning(f"المبلغ في الطلب {order_id} هو {order_amount} وليس {amount}")
                return False

            # تحديث حالة المعاملة في قاعدة البيانات
            new_status = 'completed' if status == 'COMPLETED' else 'approved'
            transaction_ref.update({
                'status': new_status,
                'verified_at': datetime.now().isoformat(),
                'payment_details': order_details
            })

            logger.info(f"تم التحقق من صحة المعاملة {transaction_id} للمستخدم {user_id} بحالة {new_status}")

            # إذا كانت الحالة COMPLETED، نقوم بتفعيل الاشتراك
            if status == 'COMPLETED':
                await activate_subscription(user_id, transaction_id)

            return True

        # إذا لم يتم توفير معرف المعاملة، نبحث عن معاملات معلقة للمستخدم
        from google.cloud.firestore_v1.base_query import FieldFilter

        transactions_ref = db.collection('transactions')

        # استخدام وسيطة filter بدلاً من where (الطريقة الموصى بها)
        query = transactions_ref.where(
            filter=FieldFilter("user_id", "==", user_id)
        ).where(
            filter=FieldFilter("status", "==", "pending")
        ).where(
            filter=FieldFilter("payment_method", "==", "paypal")
        )

        pending_transactions = list(query.get())
        if not pending_transactions:
            logger.warning(f"لا توجد معاملات معلقة للمستخدم {user_id}")
            return False

        # التحقق من كل معاملة معلقة
        for transaction_doc in pending_transactions:
            transaction_id = transaction_doc.id

            # التحقق من المعاملة
            if await verify_paypal_transaction(user_id, amount, transaction_id):
                return True

        return False
    except Exception as e:
        logger.error(f"خطأ في التحقق من معاملة PayPal: {str(e)}")
        return False

async def activate_subscription(user_id, transaction_id):
    """تفعيل الاشتراك بعد التحقق من الدفع"""
    try:
        from firebase_admin import firestore
        db = firestore.client()

        # التحقق من حالة المعاملة
        transaction_ref = db.collection('transactions').document(transaction_id)
        transaction_doc = transaction_ref.get()

        if not transaction_doc.exists:
            logger.warning(f"المعاملة {transaction_id} غير موجودة")
            return False

        transaction_data = transaction_doc.to_dict()

        # التحقق من أن المعاملة تخص المستخدم
        if transaction_data.get('user_id') != user_id:
            logger.warning(f"المعاملة {transaction_id} لا تخص المستخدم {user_id}")
            return False

        # التحقق من أن المعاملة لم تستخدم من قبل
        if transaction_data.get('used', False):
            logger.warning(f"المعاملة {transaction_id} تم استخدامها بالفعل")
            return False

        # تحديث حالة المعاملة
        transaction_ref.update({
            'used': True,
            'activated_at': datetime.now().isoformat()
        })

        # تفعيل الاشتراك
        from main import subscription_system

        # حساب تاريخ انتهاء الاشتراك (أسبوع واحد من الآن)
        expiry_date = datetime.now() + timedelta(days=7)

        # تفعيل الاشتراك
        subscription_system.set_subscription_status_internal(
            user_id,
            is_subscribed=True,
            expiry_date=expiry_date.isoformat(),
            transaction_id=transaction_id
        )

        logger.info(f"تم تفعيل اشتراك المستخدم {user_id} بنجاح")
        return True
    except Exception as e:
        logger.error(f"خطأ في تفعيل الاشتراك: {str(e)}")
        return False

class AutomaticPaymentVerifier:
    """نظام التحقق التلقائي من المدفوعات"""

    def __init__(self, db, paypal_client_id, paypal_secret, is_sandbox=False):
        """تهيئة نظام التحقق التلقائي من المدفوعات"""
        self.db = db
        self.paypal_api = PayPalAPI(paypal_client_id, paypal_secret, is_sandbox)
        self.verification_interval = 300  # 5 دقائق
        self.max_verification_attempts = 12  # 12 محاولة (ساعة واحدة)
        self.cleanup_interval = 3600  # ساعة واحدة
        self.transaction_expiry_hours = 24  # 24 ساعة
        self.scheduler = None
        # لا نقوم بتشغيل المجدول تلقائياً عند التهيئة

    async def start(self):
        """بدء نظام التحقق التلقائي"""
        from apscheduler.schedulers.asyncio import AsyncIOScheduler

        # استخدام منطقة زمنية من مكتبة pytz
        self.scheduler = AsyncIOScheduler(timezone=pytz.UTC)

        # جدولة التحقق من المعاملات المعلقة
        self.scheduler.add_job(
            self._verify_pending_transactions,
            'interval',
            seconds=self.verification_interval,
            id='verify_pending_transactions'
        )

        # جدولة تنظيف المعاملات المعلقة القديمة
        self.scheduler.add_job(
            self._cleanup_expired_transactions,
            'interval',
            seconds=self.cleanup_interval,
            id='cleanup_expired_transactions'
        )

        self.scheduler.start()
        logger.info("تم بدء نظام التحقق التلقائي من المدفوعات")

    async def stop(self):
        """إيقاف نظام التحقق التلقائي"""
        if self.scheduler:
            self.scheduler.shutdown()
            logger.info("تم إيقاف نظام التحقق التلقائي من المدفوعات")

    async def _verify_pending_transactions(self):
        """التحقق من المعاملات المعلقة"""
        try:
            # الحصول على المعاملات المعلقة
            transactions_ref = self.db.collection('transactions')

            # استخدام وسيطة filter بدلاً من where (الطريقة الموصى بها)
            from google.cloud.firestore_v1.base_query import FieldFilter
            query = transactions_ref.where(
                filter=FieldFilter("status", "==", "pending")
            ).where(
                filter=FieldFilter("payment_method", "==", "paypal")
            )

            pending_transactions = list(query.get())
            logger.info(f"تم العثور على {len(pending_transactions)} معاملة معلقة")

            for transaction_doc in pending_transactions:
                transaction_data = transaction_doc.to_dict()
                transaction_id = transaction_doc.id
                user_id = transaction_data.get('user_id')

                # التحقق من عدد محاولات التحقق
                verification_attempts = transaction_data.get('verification_attempts', 0)
                if verification_attempts >= self.max_verification_attempts:
                    logger.warning(f"تم تجاوز الحد الأقصى لمحاولات التحقق للمعاملة {transaction_id}")

                    # تحديث حالة المعاملة
                    transaction_doc.reference.update({
                        'status': 'expired',
                        'expired_at': datetime.now().isoformat()
                    })
                    continue

                # التحقق من تاريخ انتهاء الصلاحية
                expires_at = transaction_data.get('expires_at')
                if expires_at and datetime.fromisoformat(expires_at) < datetime.now():
                    logger.warning(f"انتهت صلاحية المعاملة {transaction_id}")

                    # تحديث حالة المعاملة
                    transaction_doc.reference.update({
                        'status': 'expired',
                        'expired_at': datetime.now().isoformat()
                    })
                    continue

                # زيادة عدد محاولات التحقق
                transaction_doc.reference.update({
                    'verification_attempts': verification_attempts + 1,
                    'last_verification': datetime.now().isoformat()
                })

                # الحصول على معرف الطلب من المعاملة
                order_id = transaction_data.get('order_id')
                if not order_id:
                    logger.warning(f"لا يوجد معرف طلب للمعاملة {transaction_id}")
                    continue

                # التحقق من المعاملة
                order_details = await self.paypal_api.verify_payment(order_id)

                if not order_details:
                    logger.warning(f"لم يتم العثور على تفاصيل الطلب {order_id}")
                    continue

                # التحقق من حالة الطلب
                status = order_details.get("status")
                if status == "COMPLETED":
                    logger.info(f"تم اكتمال المعاملة {transaction_id} (الطلب {order_id})")

                    # تحديث حالة المعاملة
                    transaction_doc.reference.update({
                        'status': 'completed',
                        'verified_at': datetime.now().isoformat(),
                        'payment_details': order_details
                    })

                    # تفعيل الاشتراك
                    await activate_subscription(user_id, transaction_id)
                elif status == "APPROVED":
                    logger.info(f"تمت الموافقة على المعاملة {transaction_id} (الطلب {order_id}) ولكن لم يتم اكتمالها بعد")

                    # تحديث حالة المعاملة
                    transaction_doc.reference.update({
                        'status': 'approved',
                        'approved_at': datetime.now().isoformat(),
                        'payment_details': order_details
                    })
        except Exception as e:
            logger.error(f"خطأ في التحقق من المعاملات المعلقة: {str(e)}")

    async def _cleanup_expired_transactions(self):
        """تنظيف المعاملات المعلقة القديمة"""
        try:
            # حساب الوقت الذي مضى عليه 24 ساعة
            expiry_time = datetime.now() - timedelta(hours=self.transaction_expiry_hours)
            expiry_time_iso = expiry_time.isoformat()

            # الحصول على المعاملات المعلقة القديمة
            transactions_ref = self.db.collection('transactions')

            # استخدام وسيطة filter بدلاً من where (الطريقة الموصى بها)
            from google.cloud.firestore_v1.base_query import FieldFilter

            # البحث عن المعاملات المعلقة التي تم إنشاؤها قبل وقت انتهاء الصلاحية
            query = transactions_ref.where(
                filter=FieldFilter("status", "==", "pending")
            ).where(
                filter=FieldFilter("created_at", "<", expiry_time_iso)
            )

            old_pending_transactions = list(query.get())
            logger.info(f"تم العثور على {len(old_pending_transactions)} معاملة معلقة قديمة")

            # تحديث حالة المعاملات المعلقة القديمة
            for transaction_doc in old_pending_transactions:
                transaction_id = transaction_doc.id

                # تحديث حالة المعاملة إلى منتهية الصلاحية
                transaction_doc.reference.update({
                    'status': 'expired',
                    'expired_at': datetime.now().isoformat(),
                    'expiry_reason': 'timeout'
                })

                logger.info(f"تم تحديث حالة المعاملة {transaction_id} إلى منتهية الصلاحية")

            # البحث عن المعاملات المعلقة التي ليس لها معرف طلب
            query = transactions_ref.where(
                filter=FieldFilter("status", "==", "pending")
            )

            pending_transactions = list(query.get())
            no_order_id_count = 0

            for transaction_doc in pending_transactions:
                transaction_data = transaction_doc.to_dict()
                if not transaction_data.get('order_id'):
                    transaction_id = transaction_doc.id

                    # تحديث حالة المعاملة إلى منتهية الصلاحية
                    transaction_doc.reference.update({
                        'status': 'expired',
                        'expired_at': datetime.now().isoformat(),
                        'expiry_reason': 'no_order_id'
                    })

                    no_order_id_count += 1
                    logger.info(f"تم تحديث حالة المعاملة {transaction_id} إلى منتهية الصلاحية (لا يوجد معرف طلب)")

            logger.info(f"تم تحديث حالة {no_order_id_count} معاملة معلقة بدون معرف طلب إلى منتهية الصلاحية")

            # البحث عن المعاملات المنتهية القديمة جداً (أكثر من أسبوع)
            very_old_expiry_time = datetime.now() - timedelta(days=7)
            very_old_expiry_time_iso = very_old_expiry_time.isoformat()

            query = transactions_ref.where(
                filter=FieldFilter("status", "==", "expired")
            ).where(
                filter=FieldFilter("expired_at", "<", very_old_expiry_time_iso)
            )

            very_old_expired_transactions = list(query.get())
            logger.info(f"تم العثور على {len(very_old_expired_transactions)} معاملة منتهية قديمة جداً")

            # حذف المعاملات المنتهية القديمة جداً
            deleted_count = 0
            for transaction_doc in very_old_expired_transactions:
                transaction_id = transaction_doc.id

                # حذف المعاملة
                transaction_doc.reference.delete()
                deleted_count += 1

            logger.info(f"تم حذف {deleted_count} معاملة منتهية قديمة جداً")

        except Exception as e:
            logger.error(f"خطأ في تنظيف المعاملات المعلقة القديمة: {str(e)}")
