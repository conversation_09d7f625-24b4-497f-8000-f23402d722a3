import logging
import asyncio
import re
from typing import Dict, Any, Optional, Tu<PERSON>

from analysis.gemini_analysis import get_user_api_client
from analysis.user_market_data import get_market_data_with_user_api
from api_manager import APIManager

# إعداد التسجيل
logger = logging.getLogger(__name__)

# مدير API
api_manager = None

def initialize(api_mgr):
    """تهيئة وحدة الدردشة مع الذكاء الاصطناعي"""
    global api_manager
    api_manager = api_mgr
    logger.info("تم تهيئة وحدة الدردشة مع الذكاء الاصطناعي")

async def chat_with_ai(user_id: str, message: str, lang: str = 'ar') -> str:
    """
    الدردشة مع الذكاء الاصطناعي

    Args:
        user_id: معرف المستخدم
        message: رسالة المستخدم
        lang: لغة الرد

    Returns:
        رد الذكاء الاصطناعي
    """
    try:
        logger.info(f"بدء محادثة مع الذكاء الاصطناعي للمستخدم {user_id}")

        # الحصول على نموذج Gemini للمستخدم
        model = await get_user_api_client(user_id, 'gemini')

        if model is None:
            logger.error(f"فشل في الحصول على نموذج Gemini للمستخدم {user_id}")
            return get_error_message(lang)

        # التحقق مما إذا كانت الرسالة تحتوي على استفسار عن عملة
        crypto_symbol = extract_crypto_symbol(message)

        if crypto_symbol:
            logger.info(f"تم اكتشاف رمز عملة في الرسالة: {crypto_symbol}")
            # الحصول على بيانات السوق للعملة
            # Obtener la moneda preferida del usuario (si está disponible)
            from main import subscription_system
            settings = subscription_system.get_user_settings(user_id)
            custom_currencies = settings.get('currencies', [])
            target_currency = custom_currencies[0] if custom_currencies else 'USD'

            market_data = await get_market_data_for_symbol(crypto_symbol, user_id, target_currency)

            if market_data:
                # إنشاء سياق محسن للذكاء الاصطناعي يتضمن بيانات السوق
                prompt = create_enhanced_prompt(message, market_data, crypto_symbol, lang)
            else:
                # إذا لم نتمكن من الحصول على بيانات السوق، نخبر المستخدم
                # قائمة بالعملات المدعومة
                supported_cryptos = [
                    'BTC', 'ETH', 'BNB', 'SOL', 'XRP', 'ADA', 'DOGE', 'SHIB', 'DOT', 'AVAX',
                    'MATIC', 'LTC', 'LINK', 'XLM', 'ATOM', 'UNI', 'ALGO', 'NEAR', 'FTM', 'HBAR'
                ]

                if lang == 'ar':
                    error_msg = f"عذرًا، لم أتمكن من العثور على بيانات للعملة {crypto_symbol}. قد تكون هذه العملة غير متوفرة في بينانس أو حدث خطأ أثناء جلب البيانات. يمكنك تجربة عملة أخرى أو طرح سؤال عام."
                    error_msg += "\n\nإليك بعض العملات المدعومة التي يمكنك السؤال عنها: " + ", ".join(supported_cryptos)
                else:
                    error_msg = f"Sorry, I couldn't find data for {crypto_symbol}. This cryptocurrency might not be available on Binance or there was an error fetching the data. You can try another cryptocurrency or ask a general question."
                    error_msg += "\n\nHere are some supported cryptocurrencies you can ask about: " + ", ".join(supported_cryptos)

                return error_msg
        else:
            # استخدام الرسالة الأصلية للدردشة العامة
            prompt = create_regular_prompt(message, lang)

        # استدعاء نموذج Gemini
        logger.info(f"جاري استدعاء نموذج Gemini للدردشة باللغة {lang}")
        try:
            # محاولة استخدام to_thread أولاً
            response_obj = await asyncio.to_thread(
                lambda: model.generate_content(prompt)
            )
            response = response_obj.text
            logger.info(f"تم الحصول على استجابة من Gemini بطول {len(response)} حرف")

            # التحقق من طول الرد - إذا كان قصيرًا جدًا، نعيد المحاولة
            min_response_length = 100  # الحد الأدنى المقبول لطول الرد
            if len(response) < min_response_length:
                logger.warning(f"استجابة Gemini قصيرة جدًا ({len(response)} حرف). محاولة إعادة الاستدعاء مع توجيهات إضافية.")

                # إضافة توجيهات إضافية للحصول على رد أكثر تفصيلاً
                enhanced_prompt = prompt + "\n\n"
                if lang == 'ar':
                    enhanced_prompt += """
يرجى تقديم إجابة مفصلة ومتكاملة. الإجابة القصيرة جدًا غير مقبولة. يجب أن تتضمن إجابتك:
1. تحليلاً مفصلاً للوضع الحالي
2. توقعات مستقبلية مع أرقام محددة
3. استراتيجيات واضحة للتداول
4. توصيات محددة بناءً على التحليل الفني
5. شرح للمخاطر المحتملة
6. استخدم الايموجي للتعبير 

يجب أن تكون الإجابة شاملة وتغطي جميع جوانب السؤال بعمق.
"""
                else:
                    enhanced_prompt += """
Please provide a detailed and comprehensive answer. Very short responses are not acceptable. Your answer must include:
1. Detailed analysis of the current situation
2. Future predictions with specific numbers
3. Clear trading strategies
4. Specific recommendations based on technical analysis
5. Explanation of potential risks
6. Use emojis to express your answer

The answer should be comprehensive and cover all aspects of the question in depth.
"""

                # إعادة استدعاء النموذج مع التوجيهات المحسنة
                logger.info("إعادة استدعاء نموذج Gemini مع توجيهات إضافية")
                try:
                    response_obj = await asyncio.to_thread(
                        lambda: model.generate_content(enhanced_prompt)
                    )
                    response = response_obj.text
                    logger.info(f"تم الحصول على استجابة محسنة من Gemini بطول {len(response)} حرف")
                except Exception as retry_error:
                    logger.error(f"فشل في إعادة استدعاء Gemini API: {str(retry_error)}")
                    # إذا فشلت المحاولة الثانية، نستخدم الاستجابة الأصلية مع رسالة توضيحية
                    if lang == 'ar':
                        response = "عذرًا، الاستجابة الأصلية كانت قصيرة جدًا وفشلت محاولة الحصول على استجابة أكثر تفصيلاً. " + response
                    else:
                        response = "Sorry, the original response was too short and the attempt to get a more detailed response failed. " + response

            # التحقق من طول الرد لتجنب خطأ Message_too_long
            max_message_length = 4096  # الحد الأقصى لطول رسالة تلغرام
            if len(response) > max_message_length:
                # تقسيم الرد إلى أجزاء
                if lang == 'ar':
                    response = response[:max_message_length-200] + "\n\n(تم اختصار الرد بسبب طوله. يرجى طرح أسئلة أكثر تحديدًا للحصول على إجابات أكثر تفصيلاً)"
                else:
                    response = response[:max_message_length-200] + "\n\n(Response was truncated due to length. Please ask more specific questions for more detailed answers)"

            return response
        except Exception as e:
            logger.error(f"خطأ في استدعاء Gemini API: {str(e)}")
            return get_error_message(lang)

    except Exception as e:
        logger.error(f"خطأ في الدردشة مع الذكاء الاصطناعي: {str(e)}")
        return get_error_message(lang)

def extract_crypto_symbol(message: str) -> Optional[str]:
    """
    استخراج رمز العملة أو السلعة من رسالة المستخدم

    Args:
        message: رسالة المستخدم

    Returns:
        رمز العملة أو السلعة أو None إذا لم يتم العثور على رمز
    """
    # قائمة بالعملات الشائعة للبحث عنها
    common_cryptos = [
        'BTC', 'ETH', 'BNB', 'SOL', 'XRP', 'ADA', 'DOGE', 'SHIB', 'DOT', 'AVAX',
        'MATIC', 'LTC', 'LINK', 'XLM', 'ATOM', 'UNI', 'ALGO', 'NEAR', 'FTM', 'HBAR',
        'PEPE', 'ARB', 'OP', 'INJ', 'SUI', 'AAVE', 'MKR', 'COMP', 'SNX', 'YFI',
        'CAKE', 'SAND', 'MANA', 'AXS', 'APE', 'GALA', 'ICP', 'FIL', 'GRT', 'RNDR'
    ]

    # قائمة بالسلع الشائعة للبحث عنها
    common_commodities = [
        'XAU', 'GOLD', 'الذهب', 'XAG', 'SILVER', 'الفضة',
        'WTI', 'OIL', 'النفط', 'BRENT', 'نفط برنت',
        'NATURAL_GAS', 'الغاز الطبيعي', 'COPPER', 'النحاس'
    ]

    # البحث عن رموز السلع الشائعة في الرسالة
    for commodity in common_commodities:
        pattern = r'\b' + re.escape(commodity) + r'\b'
        if re.search(pattern, message.upper()) or re.search(pattern, message):
            # إرجاع رمز السلعة كما هو (سيتم معالجته لاحقًا)
            if commodity in ['الذهب', 'GOLD']:
                return 'XAU'
            elif commodity in ['الفضة', 'SILVER']:
                return 'XAG'
            elif commodity in ['النفط', 'OIL']:
                return 'WTI'
            elif commodity == 'نفط برنت':
                return 'BRENT'
            elif commodity == 'الغاز الطبيعي':
                return 'NATURAL_GAS'
            elif commodity == 'النحاس':
                return 'COPPER'
            else:
                return commodity

    # البحث عن رموز العملات الشائعة في الرسالة
    for crypto in common_cryptos:
        pattern = r'\b' + re.escape(crypto) + r'\b'
        if re.search(pattern, message.upper()):
            return crypto + 'USDT'  # إضافة USDT للحصول على زوج التداول

    # البحث عن أنماط مثل BTC/USDT أو BTC-USDT
    pairs_pattern = r'\b([A-Za-z0-9]{2,10})[\s/\-_]+(USDT|USD|BTC|ETH)\b'
    match = re.search(pairs_pattern, message.upper())
    if match:
        return match.group(1) + match.group(2)

    return None

async def get_market_data_for_symbol(symbol: str, user_id: str, target_currency: str = 'USD') -> Optional[Dict[str, Any]]:
    """
    الحصول على بيانات السوق لرمز عملة أو سلعة معينة

    Args:
        symbol: رمز العملة أو السلعة
        user_id: معرف المستخدم
        target_currency: العملة المستهدفة للتحويل (الافتراضية: USD)

    Returns:
        بيانات السوق أو None إذا لم يتم العثور على بيانات
    """
    try:
        logger.info(f"جاري الحصول على بيانات السوق للرمز {symbol} للمستخدم {user_id}")

        # تم إزالة دعم السلع من البوت
        # التحقق مما إذا كان الرمز يمثل سلعة (XAU, XAG, WTI, BRENT, etc.)
        if symbol in ['XAU', 'XAG', 'WTI', 'BRENT', 'NATURAL_GAS', 'COPPER']:
            logger.warning(f"الرمز {symbol} يمثل سلعة، وقد تم إزالة دعم السلع من البوت")
            return None

        # إذا لم يكن الرمز سلعة، فهو عملة رقمية
        logger.info(f"الرمز {symbol} يمثل عملة رقمية، سيتم استخدام API بينانس")

        # محاولة استخدام مفاتيح API الخاصة بالمستخدم أولاً
        if api_manager:
            # الحصول على مفاتيح Binance API للمستخدم
            binance_key, binance_secret = await api_manager.get_api_keys(user_id, 'binance')

            if binance_key and binance_secret:
                logger.info(f"استخدام مفاتيح Binance API الخاصة بالمستخدم {user_id}")
                # إنشاء عميل Binance
                from binance.client import Client
                client = Client(binance_key, binance_secret)

                # الحصول على بيانات السوق باستخدام API المستخدم
                market_data = await get_market_data_with_user_api(client, symbol, target_currency=target_currency)

                if market_data and 'price' in market_data:
                    logger.info(f"تم الحصول على بيانات السوق للعملة {symbol} باستخدام API المستخدم: السعر={market_data['price']}")
                    return market_data
                else:
                    logger.warning(f"فشل في الحصول على بيانات السوق باستخدام API المستخدم، سيتم استخدام API العام")

        # استخدام API العام لبينانس إذا لم تكن هناك مفاتيح API للمستخدم أو فشلت المحاولة
        logger.info(f"استخدام API العام لبينانس للحصول على بيانات السوق للعملة {symbol}")

        # استخدام API العام لبينانس مباشرة
        market_data = await get_market_data_with_public_api(symbol, target_currency=target_currency)

        if market_data and 'price' in market_data:
            logger.info(f"تم الحصول على بيانات السوق للعملة {symbol} باستخدام API العام: السعر={market_data['price']}")
            return market_data
        else:
            logger.warning(f"لم يتم العثور على بيانات لـ {symbol} باستخدام API العام")
            return None

    except Exception as e:
        logger.error(f"خطأ في الحصول على بيانات السوق للرمز {symbol}: {str(e)}")
        return None

async def get_market_data_with_public_api(symbol: str, interval: str = '1d', limit: int = 100, target_currency: str = 'USD') -> Optional[Dict[str, Any]]:
    """
    الحصول على بيانات السوق باستخدام API العام لبينانس

    Args:
        symbol: رمز العملة
        interval: الإطار الزمني
        limit: عدد الشموع المطلوبة
        target_currency: العملة المستهدفة للتحويل (الافتراضية: USD)

    Returns:
        قاموس يحتوي على بيانات السوق أو None إذا فشل الحصول على البيانات
    """
    try:
        import aiohttp
        import pandas as pd
        from datetime import datetime, timedelta

        # التأكد من أن الرمز يحتوي على USDT
        if not symbol.endswith('USDT'):
            symbol = f"{symbol}USDT"

        # التحقق من توفر العملة أولاً
        base_url = "https://api.binance.com/api/v3"

        # التحقق من وجود العملة في بينانس
        url = f"{base_url}/exchangeInfo"
        params = {'symbol': symbol}

        async with aiohttp.ClientSession() as session:
            async with session.get(url, params=params, timeout=10) as response:
                if response.status == 200:
                    data = await response.json()
                    symbols = data.get('symbols', [])

                    # التحقق من وجود العملة وأنها متاحة للتداول
                    symbol_found = False
                    for symbol_info in symbols:
                        if symbol_info.get('symbol') == symbol and symbol_info.get('status') == 'TRADING':
                            symbol_found = True
                            break

                    if not symbol_found:
                        logger.warning(f"العملة {symbol} غير متاحة للتداول في بينانس")
                        return None
                else:
                    logger.error(f"خطأ في التحقق من توفر العملة: {response.status}")
                    return None

        # الحصول على بيانات الشموع من API العام لبينانس
        url = f"{base_url}/klines"
        params = {
            'symbol': symbol,
            'interval': interval,
            'limit': limit
        }

        async with aiohttp.ClientSession() as session:
            async with session.get(url, params=params, timeout=10) as response:
                if response.status == 200:
                    klines = await response.json()
                else:
                    logger.error(f"خطأ في الحصول على بيانات الشموع: {response.status}")
                    return None

        if not klines:
            logger.warning(f"لم يتم العثور على بيانات لـ {symbol}")
            return None

        # تحويل البيانات إلى DataFrame
        df = pd.DataFrame(klines, columns=[
            'timestamp', 'open', 'high', 'low', 'close', 'volume',
            'close_time', 'quote_asset_volume', 'number_of_trades',
            'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
        ])

        # تحويل أنواع البيانات
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        for col in ['open', 'high', 'low', 'close', 'volume']:
            df[col] = df[col].astype(float)

        # تعيين الفهرس
        df.set_index('timestamp', inplace=True)

        # الحصول على السعر الحالي والتغير
        current_price = float(df['close'].iloc[-1])
        previous_price = float(df['close'].iloc[-2])
        price_change = ((current_price - previous_price) / previous_price) * 100

        # الحصول على معلومات العملة من API العام
        url = f"{base_url}/ticker/24hr"
        params = {'symbol': symbol}

        async with aiohttp.ClientSession() as session:
            async with session.get(url, params=params, timeout=10) as response:
                if response.status == 200:
                    ticker = await response.json()
                else:
                    logger.error(f"خطأ في الحصول على معلومات العملة: {response.status}")
                    ticker = {}

        # إنشاء قاموس النتائج
        market_data = {
            'symbol': symbol,
            'price': current_price,
            'price_change': price_change,
            'currency': target_currency,
            'df': df,
            'chart_path': None
        }

        # إضافة معلومات إضافية إذا كانت متاحة
        if ticker:
            try:
                market_data['volume'] = float(ticker.get('volume', 0))
                market_data['high_24h'] = float(ticker.get('highPrice', 0))
                market_data['low_24h'] = float(ticker.get('lowPrice', 0))
            except (ValueError, KeyError) as e:
                logger.warning(f"خطأ في استخراج معلومات إضافية: {str(e)}")

        return market_data

    except Exception as e:
        logger.error(f"خطأ في الحصول على بيانات السوق باستخدام API العام: {str(e)}")
        return None

def create_enhanced_prompt(message: str, market_data: Dict[str, Any], symbol: str, lang: str) -> str:
    """
    إنشاء سياق محسن للذكاء الاصطناعي يتضمن بيانات السوق

    Args:
        message: رسالة المستخدم
        market_data: بيانات السوق
        symbol: رمز العملة أو السلعة
        lang: لغة الرد

    Returns:
        سياق محسن للذكاء الاصطناعي
    """
    # استخراج البيانات المهمة
    price = market_data.get('price', 'غير متوفر')
    price_change = market_data.get('price_change', 'غير متوفر')
    if 'change_percent' in market_data and price_change == 'غير متوفر':
        price_change = market_data.get('change_percent', 'غير متوفر')

    # استخراج بيانات إضافية إذا كانت متاحة
    volume = market_data.get('volume', 'غير متوفر')
    high_24h = market_data.get('high_24h', 'غير متوفر')
    low_24h = market_data.get('low_24h', 'غير متوفر')

    # استخراج اسم الأصل
    asset_name = market_data.get('name', symbol)

    # التحقق مما إذا كان الأصل سلعة
    is_commodity_asset = False
    if 'name' in market_data and market_data['name'] in ['الذهب', 'الفضة', 'النفط', 'نفط برنت', 'الغاز الطبيعي', 'النحاس']:
        is_commodity_asset = True

    # استخراج المؤشرات الفنية إذا كانت متاحة
    technical_indicators = market_data.get('technical_indicators', {})
    technical_analysis = market_data.get('technical_analysis', {})

    # الحصول على التاريخ الحالي
    from datetime import datetime
    current_date = datetime.now().strftime("%d %B %Y")

    # إنشاء سياق بناءً على اللغة
    if lang == 'ar':
        # تحديد نوع الأصل في النص
        if is_commodity_asset:
            asset_type = "السلع"
            asset_description = f"سلعة {asset_name}"
        else:
            asset_type = "العملات الرقمية"
            asset_description = f"العملة {symbol}"

        prompt = f"""أنت مساعد ذكي متخصص في تحليل {asset_type} والتداول. المستخدم يسأل عن {asset_description}.

معلومات مهمة:
- التاريخ الحالي هو: {current_date}
- يجب أن تستخدم هذا التاريخ في تحليلاتك وإجاباتك
- يجب أن تكون إجابتك أقل من 4000 حرف لكن مع الحفاظ على التفاصيل المهمة

معلومات السوق الحالية:
- الرمز: {symbol}
- الاسم: {asset_name}
- السعر الحالي: {price} دولار
- نسبة التغير: {price_change}%
- حجم التداول: {volume}
"""

        # إضافة معلومات إضافية إذا كانت متاحة
        if high_24h != 'غير متوفر':
            prompt += f"- أعلى سعر (24 ساعة): {high_24h} دولار\n"
        if low_24h != 'غير متوفر':
            prompt += f"- أدنى سعر (24 ساعة): {low_24h} دولار\n"

        # إضافة المؤشرات الفنية إذا كانت متاحة
        if technical_indicators:
            prompt += "\nالمؤشرات الفنية الحالية:\n"
            if 'rsi_14' in technical_indicators:
                prompt += f"- مؤشر القوة النسبية (RSI): {technical_indicators['rsi_14']:.2f}\n"
            if 'ema_9' in technical_indicators:
                prompt += f"- المتوسط المتحرك الأسي (EMA 9): {technical_indicators['ema_9']:.2f}\n"
            if 'ema_20' in technical_indicators:
                prompt += f"- المتوسط المتحرك الأسي (EMA 20): {technical_indicators['ema_20']:.2f}\n"
            if 'ema_50' in technical_indicators:
                prompt += f"- المتوسط المتحرك الأسي (EMA 50): {technical_indicators['ema_50']:.2f}\n"
            if 'ema_200' in technical_indicators:
                prompt += f"- المتوسط المتحرك الأسي (EMA 200): {technical_indicators['ema_200']:.2f}\n"
            if 'macd' in technical_indicators:
                prompt += f"- مؤشر MACD: {technical_indicators['macd']:.2f}\n"
            if 'macd_signal' in technical_indicators:
                prompt += f"- إشارة MACD: {technical_indicators['macd_signal']:.2f}\n"
            if 'bollinger_upper' in technical_indicators:
                prompt += f"- نطاق بولينجر العلوي: {technical_indicators['bollinger_upper']:.2f}\n"
            if 'bollinger_middle' in technical_indicators:
                prompt += f"- نطاق بولينجر المتوسط: {technical_indicators['bollinger_middle']:.2f}\n"
            if 'bollinger_lower' in technical_indicators:
                prompt += f"- نطاق بولينجر السفلي: {technical_indicators['bollinger_lower']:.2f}\n"

        # إضافة التحليل الفني إذا كان متاحًا
        if technical_analysis:
            prompt += "\nالتحليل الفني الحالي:\n"
            if 'trend_long' in technical_analysis:
                prompt += f"- الاتجاه طويل المدى: {technical_analysis['trend_long']}\n"
            if 'trend_medium' in technical_analysis:
                prompt += f"- الاتجاه متوسط المدى: {technical_analysis['trend_medium']}\n"
            if 'trend_short' in technical_analysis:
                prompt += f"- الاتجاه قصير المدى: {technical_analysis['trend_short']}\n"
            if 'rsi' in technical_analysis:
                prompt += f"- تحليل RSI: {technical_analysis['rsi']}\n"
            if 'macd' in technical_analysis:
                prompt += f"- تحليل MACD: {technical_analysis['macd']}\n"
            if 'bollinger' in technical_analysis:
                prompt += f"- تحليل نطاقات بولينجر: {technical_analysis['bollinger']}\n"
            if 'recommendation' in technical_analysis:
                prompt += f"- التوصية العامة: {technical_analysis['recommendation']}\n"

        prompt += """
تعليمات مهمة للتحليل:
1. قدم تحليلاً تقنياً مفصلاً يتضمن مستويات الدعم والمقاومة المحددة بالأرقام
2. حدد السيناريوهات السعرية المحتملة (صعودي، هبوطي، جانبي) مع تحديد المستويات السعرية لكل سيناريو
3. اقترح استراتيجيات تداول محددة مع نقاط دخول وخروج وأوامر وقف الخسارة
4. قدم توصية واضحة ومحددة بناءً على التحليل الفني

قم بتحليل هذه المعلومات في الخلفية دون ذكر أنك قمت بتحليلها، ثم أجب على سؤال المستخدم بطريقة طبيعية ومفيدة.

سؤال المستخدم: {message}

ملاحظة: لا تذكر أنك تلقيت بيانات السوق مني، وتعامل مع الأمر كما لو كنت تعرف هذه المعلومات بالفعل. أجب بشكل طبيعي ومباشر على سؤال المستخدم.
"""
    else:
        # تحديد نوع الأصل في النص
        if is_commodity_asset:
            asset_type = "commodities"
            asset_description = f"commodity {asset_name}"
        else:
            asset_type = "cryptocurrencies"
            asset_description = f"cryptocurrency {symbol}"

        prompt = f"""You are an intelligent assistant specializing in {asset_type} analysis and trading. The user is asking about the {asset_description}.

Important information:
- Today's date is: {current_date}
- You must use this date in your analyses and responses
- Your answer should be less than 4000 characters but maintain important details

Current market information:
- Symbol: {symbol}
- Name: {asset_name}
- Current price: ${price}
- Change percentage: {price_change}%
- Trading volume: {volume}
"""

        # إضافة معلومات إضافية إذا كانت متاحة
        if high_24h != 'غير متوفر':
            prompt += f"- Highest price (24h): ${high_24h}\n"
        if low_24h != 'غير متوفر':
            prompt += f"- Lowest price (24h): ${low_24h}\n"

        # إضافة المؤشرات الفنية إذا كانت متاحة
        if technical_indicators:
            prompt += "\nCurrent Technical Indicators:\n"
            if 'rsi_14' in technical_indicators:
                prompt += f"- Relative Strength Index (RSI): {technical_indicators['rsi_14']:.2f}\n"
            if 'ema_9' in technical_indicators:
                prompt += f"- Exponential Moving Average (EMA 9): {technical_indicators['ema_9']:.2f}\n"
            if 'ema_20' in technical_indicators:
                prompt += f"- Exponential Moving Average (EMA 20): {technical_indicators['ema_20']:.2f}\n"
            if 'ema_50' in technical_indicators:
                prompt += f"- Exponential Moving Average (EMA 50): {technical_indicators['ema_50']:.2f}\n"
            if 'ema_200' in technical_indicators:
                prompt += f"- Exponential Moving Average (EMA 200): {technical_indicators['ema_200']:.2f}\n"
            if 'macd' in technical_indicators:
                prompt += f"- MACD: {technical_indicators['macd']:.2f}\n"
            if 'macd_signal' in technical_indicators:
                prompt += f"- MACD Signal: {technical_indicators['macd_signal']:.2f}\n"
            if 'bollinger_upper' in technical_indicators:
                prompt += f"- Bollinger Upper Band: {technical_indicators['bollinger_upper']:.2f}\n"
            if 'bollinger_middle' in technical_indicators:
                prompt += f"- Bollinger Middle Band: {technical_indicators['bollinger_middle']:.2f}\n"
            if 'bollinger_lower' in technical_indicators:
                prompt += f"- Bollinger Lower Band: {technical_indicators['bollinger_lower']:.2f}\n"

        # إضافة التحليل الفني إذا كان متاحًا
        if technical_analysis:
            prompt += "\nCurrent Technical Analysis:\n"
            if 'trend_long' in technical_analysis:
                prompt += f"- Long-term Trend: {technical_analysis['trend_long']}\n"
            if 'trend_medium' in technical_analysis:
                prompt += f"- Medium-term Trend: {technical_analysis['trend_medium']}\n"
            if 'trend_short' in technical_analysis:
                prompt += f"- Short-term Trend: {technical_analysis['trend_short']}\n"
            if 'rsi' in technical_analysis:
                prompt += f"- RSI Analysis: {technical_analysis['rsi']}\n"
            if 'macd' in technical_analysis:
                prompt += f"- MACD Analysis: {technical_analysis['macd']}\n"
            if 'bollinger' in technical_analysis:
                prompt += f"- Bollinger Bands Analysis: {technical_analysis['bollinger']}\n"
            if 'recommendation' in technical_analysis:
                prompt += f"- Overall Recommendation: {technical_analysis['recommendation']}\n"

        prompt += """
Important analysis instructions:
1. Provide detailed technical analysis including specific support and resistance levels with exact numbers
2. Identify potential price scenarios (bullish, bearish, sideways) with specific price levels for each scenario
3. Suggest specific trading strategies with entry/exit points and stop-loss orders
4. Give a clear and specific recommendation based on technical analysis

Analyze this information in the background without mentioning that you analyzed it, then answer the user's question in a natural and helpful way.

User's question: {message}

Note: Do not mention that you received market data from me, and act as if you already know this information. Answer the user's question naturally and directly.
"""

    return prompt

def create_regular_prompt(message: str, lang: str) -> str:
    """
    إنشاء سياق عادي للذكاء الاصطناعي للدردشة العامة

    Args:
        message: رسالة المستخدم
        lang: لغة الرد

    Returns:
        سياق للذكاء الاصطناعي
    """
    # الحصول على التاريخ الحالي
    from datetime import datetime
    current_date = datetime.now().strftime("%d %B %Y")

    if lang == 'ar':
        prompt = f"""أنت مساعد ذكي متخصص في التداول والعملات الرقمية والسلع والتحليل الفني. أجب على سؤال المستخدم بشكل مفيد ودقيق.

معلومات مهمة:
- التاريخ الحالي هو: {current_date}
- يجب أن تستخدم هذا التاريخ في تحليلاتك وإجاباتك إذا كان ذلك مناسباً
- يجب أن تكون إجابتك أقل من 4000 حرف لكن مع الحفاظ على التفاصيل المهمة

تعليمات مهمة للتحليل (إذا كان السؤال يتعلق بتحليل العملات أو السلع):
1. قدم تحليلاً تقنياً مفصلاً يتضمن مستويات الدعم والمقاومة المحددة بالأرقام
2. حدد السيناريوهات السعرية المحتملة (صعودي، هبوطي، جانبي) مع تحديد المستويات السعرية لكل سيناريو
3. اقترح استراتيجيات تداول محددة مع نقاط دخول وخروج وأوامر وقف الخسارة
4. قدم توصية واضحة ومحددة بناءً على التحليل الفني

ملاحظة: أنت قادر على تحليل العملات الرقمية (مثل بيتكوين وإيثريوم) والسلع (مثل الذهب والفضة والنفط).

سؤال المستخدم: {message}
"""
    else:
        prompt = f"""You are an intelligent assistant specializing in trading, cryptocurrencies, commodities, and technical analysis. Answer the user's question in a helpful and accurate way.

Important information:
- Today's date is: {current_date}
- You must use this date in your analyses and responses when appropriate
- Your answer should be less than 4000 characters but maintain important details

Important analysis instructions (if the question is about cryptocurrency or commodity analysis):
1. Provide detailed technical analysis including specific support and resistance levels with exact numbers
2. Identify potential price scenarios (bullish, bearish, sideways) with specific price levels for each scenario
3. Suggest specific trading strategies with entry/exit points and stop-loss orders
4. Give a clear and specific recommendation based on technical analysis

Note: You are capable of analyzing cryptocurrencies (like Bitcoin and Ethereum) and commodities (like gold, silver, and oil).

User's question: {message}
"""

    return prompt

def get_error_message(lang: str) -> str:
    """
    الحصول على رسالة خطأ بناءً على اللغة

    Args:
        lang: لغة الرسالة

    Returns:
        رسالة الخطأ
    """
    if lang == 'ar':
        return "⚠️ عذراً، حدث خطأ أثناء الاتصال بالذكاء الاصطناعي. يرجى المحاولة مرة أخرى لاحقاً."
    else:
        return "⚠️ Sorry, an error occurred while connecting to the AI. Please try again later."
