# معلومات البوت
BOT_TOKEN=your_telegram_bot_token
OWNER_ID=your_telegram_id

# معلومات Binance API
BINANCE_API_KEY=your_binance_api_key
BINANCE_API_SECRET=your_binance_api_secret

# معلومات Gemini API
# يمكنك الحصول على مفتاح Gemini API من https://ai.google.dev/
# تأكد من أن المفتاح يدعم نموذج gemini-2.0-flash
GEMINI_API_KEY=your_gemini_api_key

# معلومات PayPal
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_client_secret
PAYPAL_LINK=your_paypal_payment_link

# معلومات التشفير
# يجب أن يكون مفتاح التشفير مشفرًا بـ base64 وبطول 44 حرفًا
# يمكنك إنشاء مفتاح جديد باستخدام Python:
# from cryptography.fernet import Fernet
# print(Fernet.generate_key().decode())
ENCRYPTION_KEY=your_encryption_key

# معلومات GitHub
GITHUB_TOKEN=your_github_token
GITHUB_REPO=your_github_repo
GITHUB_OWNER=your_github_username