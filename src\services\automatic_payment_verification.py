"""
وحدة التحقق التلقائي من المدفوعات والتجديد التلقائي للاشتراكات
"""

import logging
import json
import asyncio
import aiohttp
import hmac
import hashlib
import base64
import os
from datetime import datetime, timedelta
from typing import Dict, Any, Tuple, Optional, List
import firebase_admin
from firebase_admin import firestore
from google.cloud.firestore_v1 import FieldFilter
import pytz
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from telegram import Bot

# إعداد التسجيل
logger = logging.getLogger(__name__)

class AutomaticPaymentVerifier:
    """
    فئة للتحقق التلقائي من المدفوعات والتجديد التلقائي للاشتراكات
    """
    def __init__(self, db, paypal_client_id: str, paypal_secret: str, is_sandbox: bool = False, bot_token: str = None):
        """
        تهيئة المتحقق التلقائي من المدفوعات

        Args:
            db: قاعدة بيانات Firestore
            paypal_client_id: معرف العميل من PayPal
            paypal_secret: المفتاح السري من PayPal
            is_sandbox: استخدام بيئة الاختبار (True) أو البيئة الحية (False)
            bot_token: رمز البوت لإرسال الإشعارات
        """
        self.db = db
        self.paypal_client_id = paypal_client_id
        self.paypal_secret = paypal_secret
        self.is_sandbox = is_sandbox
        self.base_url = "https://api-m.sandbox.paypal.com" if is_sandbox else "https://api-m.paypal.com"
        self.scheduler = AsyncIOScheduler(timezone=pytz.UTC)
        self.verification_interval = 5  # دقائق
        self.max_verification_attempts = 12  # 12 محاولة (ساعة واحدة)
        self.bot_token = bot_token
        self.bot = Bot(token=bot_token) if bot_token else None
        self.subscription_price = 5.0  # USD
        self.subscription_duration = 7  # أيام

        logger.info(f"تم تهيئة المتحقق التلقائي من المدفوعات في وضع {'الاختبار' if is_sandbox else 'الإنتاج'}")

    async def start(self):
        """
        بدء المتحقق التلقائي من المدفوعات والتجديد التلقائي للاشتراكات
        """
        if not self.scheduler.running:
            # إضافة مهمة التحقق الدوري من المدفوعات
            self.scheduler.add_job(
                self.verify_pending_transactions,
                'interval',
                minutes=self.verification_interval,
                id='verify_pending_transactions'
            )

            # إضافة مهمة التحقق من الاشتراكات المنتهية
            self.scheduler.add_job(
                self.check_expiring_subscriptions,
                'interval',
                hours=6,  # كل 6 ساعات
                id='check_expiring_subscriptions'
            )

            # إضافة مهمة إرسال الإشعارات
            self.scheduler.add_job(
                self.send_pending_notifications,
                'interval',
                minutes=10,  # كل 10 دقائق
                id='send_pending_notifications'
            )

            # بدء المجدول
            self.scheduler.start()
            logger.info(f"تم بدء المتحقق التلقائي من المدفوعات والتجديد التلقائي للاشتراكات")

    async def stop(self):
        """
        إيقاف المتحقق التلقائي من المدفوعات
        """
        if self.scheduler.running:
            self.scheduler.shutdown()
            logger.info("تم إيقاف المتحقق التلقائي من المدفوعات")

    async def get_access_token(self) -> str:
        """
        الحصول على رمز الوصول من PayPal API

        Returns:
            رمز الوصول

        Raises:
            Exception: إذا فشل الحصول على رمز الوصول
        """
        try:
            # إنشاء رأس الطلب للمصادقة
            auth = aiohttp.BasicAuth(self.paypal_client_id, self.paypal_secret)

            # الحصول على رمز الوصول
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f'{self.base_url}/v1/oauth2/token',
                    auth=auth,
                    data={'grant_type': 'client_credentials'},
                    timeout=30
                ) as response:
                    if response.status != 200:
                        logger.error(f"خطأ في الحصول على رمز الوصول: {await response.text()}")
                        raise Exception(f"خطأ في الحصول على رمز الوصول: {response.status}")

                    token_data = await response.json()
                    return token_data['access_token']

        except Exception as e:
            logger.error(f"خطأ في الحصول على رمز الوصول: {str(e)}")
            raise

    async def verify_transaction(self, transaction_id: str, user_id: str, amount: float = 5.0) -> bool:
        """
        التحقق من معاملة PayPal

        Args:
            transaction_id: معرف المعاملة في قاعدة البيانات
            user_id: معرف المستخدم
            amount: المبلغ المتوقع

        Returns:
            نجاح التحقق
        """
        try:
            # الحصول على بيانات المعاملة من قاعدة البيانات
            transaction_ref = self.db.collection('transactions').document(transaction_id)
            transaction_data = transaction_ref.get()

            if not transaction_data.exists:
                logger.error(f"المعاملة {transaction_id} غير موجودة")
                return False

            transaction = transaction_data.to_dict()

            # التحقق من أن المعاملة تخص المستخدم المحدد
            if transaction.get('user_id') != user_id:
                logger.error(f"المعاملة {transaction_id} لا تخص المستخدم {user_id}")
                return False

            # التحقق من أن المعاملة لم يتم استخدامها من قبل
            if transaction.get('used', False):
                logger.error(f"المعاملة {transaction_id} تم استخدامها بالفعل")
                return False

            # التحقق من حالة المعاملة
            if transaction.get('status') == 'completed':
                logger.info(f"المعاملة {transaction_id} مكتملة بالفعل")
                return True

            # زيادة عدد محاولات التحقق
            verification_attempts = transaction.get('verification_attempts', 0) + 1

            # تحديث بيانات المعاملة
            transaction_ref.update({
                'verification_attempts': verification_attempts,
                'last_verification': datetime.now().isoformat()
            })

            # التحقق من عدد محاولات التحقق
            if verification_attempts > self.max_verification_attempts:
                logger.warning(f"تم تجاوز الحد الأقصى لمحاولات التحقق للمعاملة {transaction_id}")
                transaction_ref.update({
                    'status': 'failed',
                    'failure_reason': 'تجاوز الحد الأقصى لمحاولات التحقق'
                })
                return False

            # الحصول على رمز الوصول
            access_token = await self.get_access_token()

            # تعديل نطاق البحث عن المعاملات (آخر 24 ساعة)
            end_time = datetime.now(pytz.UTC)
            start_time = end_time - timedelta(days=1)

            # تنسيق التواريخ بشكل صحيح
            start_date = start_time.strftime('%Y-%m-%dT%H:%M:%S-0000')
            end_date = end_time.strftime('%Y-%m-%dT%H:%M:%S-0000')

            headers = {
                'Authorization': f'Bearer {access_token}',
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }

            # البحث عن المعاملات
            async with aiohttp.ClientSession() as session:
                params = {
                    'start_date': start_date,
                    'end_date': end_date,
                    'fields': 'all',
                    'page_size': 100,
                    'page': 1
                }

                logger.info(f"جاري البحث عن المعاملات من {start_date} إلى {end_date}")

                async with session.get(
                    f'{self.base_url}/v1/reporting/transactions',
                    headers=headers,
                    params=params,
                    timeout=30
                ) as response:
                    if response.status != 200:
                        logger.error(f"خطأ في البحث عن المعاملات: {await response.text()}")
                        return False

                    transactions = await response.json()

                    # البحث عن المعاملة المطلوبة
                    for paypal_transaction in transactions.get('transaction_details', []):
                        transaction_info = paypal_transaction.get('transaction_info', {})
                        payer_info = paypal_transaction.get('payer_info', {})

                        # محاولة العثور على معرف المستخدم في أماكن مختلفة
                        note_user_id = payer_info.get('note', '').strip()
                        custom_field = transaction_info.get('custom_field', '').strip()
                        transaction_note = transaction_info.get('transaction_note', '').strip()

                        found_user_id = note_user_id or custom_field or transaction_note

                        # التحقق من المعاملة
                        if (
                            transaction_info.get('transaction_status') == 'S' and  # ناجحة
                            abs(float(transaction_info.get('transaction_amount', {}).get('value', 0)) - amount) < 0.01 and  # المبلغ صحيح
                            found_user_id == user_id
                        ):
                            # تحديث حالة المعاملة
                            transaction_ref.update({
                                'status': 'completed',
                                'completed_at': datetime.now().isoformat(),
                                'paypal_transaction_id': transaction_info.get('transaction_id', ''),
                                'verification_method': 'automatic'
                            })

                            logger.info(f"✅ تم التحقق من معاملة PayPal للمستخدم {user_id}")

                            # تفعيل الاشتراك
                            await self.activate_subscription(user_id, transaction_id)

                            return True

            logger.warning(f"❌ لم يتم العثور على معاملة PayPal صالحة للمستخدم {user_id}")
            return False

        except Exception as e:
            logger.error(f"خطأ في التحقق من معاملة PayPal: {str(e)}")
            return False

    async def verify_pending_transactions(self):
        """
        التحقق من جميع المعاملات المعلقة
        """
        try:
            logger.info("بدء التحقق الدوري من المعاملات المعلقة")

            # الحصول على المعاملات المعلقة
            pending_transactions = self.db.collection('transactions').where('status', '==', 'pending').get()

            if not pending_transactions:
                logger.info("لا توجد معاملات معلقة للتحقق منها")
                return

            # التحقق من كل معاملة
            for transaction in pending_transactions:
                transaction_data = transaction.to_dict()
                user_id = transaction_data.get('user_id')

                if not user_id:
                    logger.error(f"المعاملة {transaction.id} لا تحتوي على معرف مستخدم")
                    continue

                # التحقق من المعاملة
                await self.verify_transaction(transaction.id, user_id, transaction_data.get('amount', 5.0))

            logger.info("انتهاء التحقق الدوري من المعاملات المعلقة")

        except Exception as e:
            logger.error(f"خطأ في التحقق من المعاملات المعلقة: {str(e)}")

    async def activate_subscription(self, user_id: str, transaction_id: str) -> bool:
        """
        تفعيل الاشتراك بعد التحقق من الدفع

        Args:
            user_id: معرف المستخدم
            transaction_id: معرف المعاملة

        Returns:
            نجاح التفعيل
        """
        try:
            # تحديث حالة المعاملة
            transaction_ref = self.db.collection('transactions').document(transaction_id)
            transaction_ref.update({
                'used': True,
                'activation_date': datetime.now().isoformat()
            })

            # استدعاء دالة تفعيل الاشتراك
            from main import activate_subscription
            success = await activate_subscription(user_id, transaction_id)

            if success:
                logger.info(f"✅ تم تفعيل اشتراك المستخدم {user_id} بنجاح")

                # إرسال إشعار للمستخدم
                await self.send_subscription_notification(user_id)

                return True
            else:
                logger.error(f"❌ فشل في تفعيل اشتراك المستخدم {user_id}")
                return False

        except Exception as e:
            logger.error(f"خطأ في تفعيل الاشتراك: {str(e)}")
            return False

    async def send_subscription_notification(self, user_id: str, notification_type: str = 'activated') -> bool:
        """
        إرسال إشعار للمستخدم بخصوص الاشتراك

        Args:
            user_id: معرف المستخدم
            notification_type: نوع الإشعار (activated, expiring_soon, expired, renewal_failed, renewal_success)

        Returns:
            نجاح الإرسال
        """
        try:
            # الحصول على لغة المستخدم
            user_settings_ref = self.db.collection('user_settings').document(user_id)
            user_settings_data = user_settings_ref.get()

            lang = 'ar'  # اللغة الافتراضية
            if user_settings_data.exists:
                lang = user_settings_data.to_dict().get('lang', 'ar')

            # إنشاء نص الإشعار حسب النوع
            notification_text = ""
            notification_type_db = ""

            if notification_type == 'activated':
                notification_text = (
                    "✅ تم تفعيل اشتراكك بنجاح!\n\n"
                    "شكراً لدعمك. يمكنك الآن الاستمتاع بجميع ميزات البوت المتقدمة."
                ) if lang == 'ar' else (
                    "✅ Your subscription has been activated successfully!\n\n"
                    "Thank you for your support. You can now enjoy all advanced features of the bot."
                )
                notification_type_db = 'subscription_activated'

            elif notification_type == 'expiring_soon':
                notification_text = (
                    "⚠️ تنبيه: اشتراكك سينتهي قريباً!\n\n"
                    "يرجى تجديد اشتراكك للاستمرار في الاستفادة من جميع الميزات المتقدمة.\n"
                    "للتجديد، استخدم الأمر /upgrade"
                ) if lang == 'ar' else (
                    "⚠️ Alert: Your subscription will expire soon!\n\n"
                    "Please renew your subscription to continue enjoying all advanced features.\n"
                    "To renew, use the /upgrade command"
                )
                notification_type_db = 'subscription_expiring'

            elif notification_type == 'expired':
                notification_text = (
                    "❌ انتهى اشتراكك!\n\n"
                    "لقد انتهت صلاحية اشتراكك. للاستمرار في استخدام الميزات المتقدمة، يرجى تجديد اشتراكك.\n"
                    "للتجديد، استخدم الأمر /upgrade"
                ) if lang == 'ar' else (
                    "❌ Your subscription has expired!\n\n"
                    "Your subscription has expired. To continue using advanced features, please renew your subscription.\n"
                    "To renew, use the /upgrade command"
                )
                notification_type_db = 'subscription_expired'

            elif notification_type == 'renewal_failed':
                notification_text = (
                    "❌ فشل تجديد الاشتراك التلقائي!\n\n"
                    "لم نتمكن من تجديد اشتراكك تلقائياً. يرجى التحقق من طريقة الدفع الخاصة بك أو تجديد الاشتراك يدوياً.\n"
                    "للتجديد، استخدم الأمر /upgrade"
                ) if lang == 'ar' else (
                    "❌ Automatic renewal failed!\n\n"
                    "We couldn't renew your subscription automatically. Please check your payment method or renew manually.\n"
                    "To renew, use the /upgrade command"
                )
                notification_type_db = 'renewal_failed'

            elif notification_type == 'renewal_success':
                notification_text = (
                    "✅ تم تجديد اشتراكك بنجاح!\n\n"
                    "تم تجديد اشتراكك تلقائياً. شكراً لاستمرار دعمك!"
                ) if lang == 'ar' else (
                    "✅ Your subscription has been renewed successfully!\n\n"
                    "Your subscription has been automatically renewed. Thank you for your continued support!"
                )
                notification_type_db = 'renewal_success'

            # إضافة الإشعار إلى قاعدة البيانات ليتم إرساله لاحقاً
            notification_ref = self.db.collection('notifications').document()
            notification_data = {
                'user_id': user_id,
                'text': notification_text,
                'created_at': datetime.now().isoformat(),
                'sent': False,
                'type': notification_type_db
            }

            notification_ref.set(notification_data)
            logger.info(f"تم إضافة إشعار {notification_type} للمستخدم {user_id}")

            # محاولة إرسال الإشعار مباشرة إذا كان البوت متاحاً
            if self.bot:
                try:
                    await self.bot.send_message(chat_id=user_id, text=notification_text)
                    # تحديث حالة الإشعار
                    notification_ref.update({'sent': True, 'sent_at': datetime.now().isoformat()})
                    logger.info(f"تم إرسال إشعار {notification_type} للمستخدم {user_id} مباشرة")
                except Exception as e:
                    logger.warning(f"لم يتم إرسال الإشعار مباشرة للمستخدم {user_id}: {str(e)}")
                    # سيتم إرسال الإشعار لاحقاً من خلال مهمة إرسال الإشعارات

            return True

        except Exception as e:
            logger.error(f"خطأ في إرسال إشعار {notification_type}: {str(e)}")
            return False

    async def send_pending_notifications(self):
        """
        إرسال الإشعارات المعلقة للمستخدمين
        """
        try:
            if not self.bot:
                logger.warning("لا يمكن إرسال الإشعارات: البوت غير متاح")
                return

            logger.info("بدء إرسال الإشعارات المعلقة")

            # الحصول على الإشعارات غير المرسلة
            notifications = self.db.collection('notifications').where('sent', '==', False).limit(50).get()

            if not notifications:
                logger.info("لا توجد إشعارات معلقة للإرسال")
                return

            for notification in notifications:
                notification_data = notification.to_dict()
                user_id = notification_data.get('user_id')
                text = notification_data.get('text')

                if not user_id or not text:
                    logger.error(f"إشعار غير صالح: {notification.id}")
                    notification.reference.delete()
                    continue

                try:
                    # إرسال الإشعار
                    await self.bot.send_message(chat_id=user_id, text=text)

                    # التحقق مما إذا كان الإشعار متعلقًا باليوم المجاني
                    notification_type = notification_data.get('type', '')
                    if notification_type.startswith('free_day_'):
                        # حذف إشعار اليوم المجاني مباشرة بعد إرساله بنجاح
                        notification.reference.delete()
                        logger.info(f"تم حذف إشعار اليوم المجاني بعد إرساله بنجاح للمستخدم {user_id}")
                    else:
                        # تحديث حالة الإشعار العادي
                        notification.reference.update({
                            'sent': True,
                            'sent_at': datetime.now().isoformat()
                        })

                    logger.info(f"تم إرسال إشعار للمستخدم {user_id}")

                    # انتظار قصير لتجنب تجاوز حدود API
                    await asyncio.sleep(0.1)

                except Exception as e:
                    logger.error(f"خطأ في إرسال إشعار للمستخدم {user_id}: {str(e)}")

                    # تحديث عدد محاولات الإرسال
                    attempts = notification_data.get('attempts', 0) + 1
                    notification.reference.update({'attempts': attempts})

                    # حذف الإشعار بعد 5 محاولات فاشلة
                    if attempts >= 5:
                        notification.reference.delete()
                        logger.warning(f"تم حذف إشعار بعد {attempts} محاولات فاشلة للمستخدم {user_id}")

            logger.info("انتهاء إرسال الإشعارات المعلقة")

        except Exception as e:
            logger.error(f"خطأ في إرسال الإشعارات المعلقة: {str(e)}")

    async def check_expiring_subscriptions(self):
        """
        التحقق من الاشتراكات التي ستنتهي قريباً وإرسال إشعارات للمستخدمين
        """
        try:
            logger.info("بدء التحقق من الاشتراكات المنتهية")

            # الحصول على جميع الاشتراكات النشطة
            subscriptions = self.db.collection('subscriptions').where('is_active', '==', True).get()

            if not subscriptions:
                logger.info("لا توجد اشتراكات نشطة للتحقق منها")
                return

            now = datetime.now()

            for subscription in subscriptions:
                subscription_data = subscription.to_dict()
                user_id = subscription.id

                # التحقق من تاريخ انتهاء الاشتراك
                try:
                    expiry_str = subscription_data.get('expiry')
                    if not expiry_str:
                        continue

                    expiry = datetime.fromisoformat(expiry_str)
                    time_left = expiry - now
                    hours_left = time_left.total_seconds() / 3600

                    # إرسال إشعارات مختلفة حسب الوقت المتبقي
                    if hours_left <= 0:
                        # الاشتراك منتهي
                        await self._update_expired_subscription(user_id)
                        await self.send_subscription_notification(user_id, 'expired')

                    elif hours_left <= 24:
                        # أقل من 24 ساعة
                        # التحقق من وجود إشعار سابق لتجنب التكرار
                        recent_notification = self.db.collection('notifications').where('user_id', '==', user_id).where('type', '==', 'subscription_expiring').where('created_at', '>', (now - timedelta(hours=12)).isoformat()).limit(1).get()

                        if not recent_notification:
                            await self.send_subscription_notification(user_id, 'expiring_soon')

                            # محاولة التجديد التلقائي إذا كان مفعلاً
                            if subscription_data.get('auto_renewal', False):
                                await self.attempt_auto_renewal(user_id)

                except Exception as e:
                    logger.error(f"خطأ في التحقق من اشتراك المستخدم {user_id}: {str(e)}")

            logger.info("انتهاء التحقق من الاشتراكات المنتهية")

        except Exception as e:
            logger.error(f"خطأ في التحقق من الاشتراكات المنتهية: {str(e)}")

    async def _update_expired_subscription(self, user_id: str):
        """
        تحديث حالة الاشتراك المنتهي

        Args:
            user_id: معرف المستخدم
        """
        try:
            # تحديث حالة الاشتراك في قاعدة البيانات
            subscription_ref = self.db.collection('subscriptions').document(user_id)
            subscription_data = subscription_ref.get()

            if not subscription_data.exists:
                return

            # تحديث حالة الاشتراك
            subscription_ref.update({
                'is_active': False,
                'status': 'expired',
                'expired_at': datetime.now().isoformat()
            })

            # تحديث حالة المستخدم
            user_ref = self.db.collection('users').document(user_id)
            user_ref.update({
                'subscriptionStatus': 'غير مشترك',
                'lastUpdated': datetime.now().isoformat()
            })

            logger.info(f"تم تحديث حالة الاشتراك المنتهي للمستخدم {user_id}")

        except Exception as e:
            logger.error(f"خطأ في تحديث حالة الاشتراك المنتهي للمستخدم {user_id}: {str(e)}")

    async def attempt_auto_renewal(self, user_id: str) -> bool:
        """
        محاولة تجديد الاشتراك تلقائياً

        Args:
            user_id: معرف المستخدم

        Returns:
            نجاح التجديد
        """
        try:
            logger.info(f"محاولة تجديد الاشتراك تلقائياً للمستخدم {user_id}")

            # التحقق من وجود طريقة دفع محفوظة
            payment_methods = self.db.collection('payment_methods').where('user_id', '==', user_id).where('is_default', '==', True).limit(1).get()

            if not payment_methods:
                logger.warning(f"لا توجد طريقة دفع محفوظة للمستخدم {user_id}")
                await self.send_subscription_notification(user_id, 'renewal_failed')
                return False

            payment_method = payment_methods[0].to_dict()
            payment_method_id = payment_method.get('payment_method_id')

            if not payment_method_id:
                logger.warning(f"معرف طريقة الدفع غير صالح للمستخدم {user_id}")
                await self.send_subscription_notification(user_id, 'renewal_failed')
                return False

            # إنشاء معاملة جديدة
            transaction_ref = self.db.collection('transactions').document()
            transaction_data = {
                'user_id': user_id,
                'amount': self.subscription_price,
                'created_at': datetime.now().isoformat(),
                'status': 'pending',
                'used': False,
                'expires_at': (datetime.now() + timedelta(hours=1)).isoformat(),
                'verification_attempts': 0,
                'payment_method': 'paypal',
                'payment_method_id': payment_method_id,
                'is_renewal': True
            }

            transaction_ref.set(transaction_data)
            transaction_id = transaction_ref.id

            # TODO: استخدام PayPal API لإجراء الدفع باستخدام طريقة الدفع المحفوظة
            # هذا يتطلب تنفيذ دالة خاصة في PayPalManager

            # للتبسيط، سنفترض أن الدفع نجح ونقوم بتفعيل الاشتراك
            # في التنفيذ الفعلي، يجب استخدام PayPal API لإجراء الدفع

            # تحديث حالة المعاملة
            transaction_ref.update({
                'status': 'completed',
                'completed_at': datetime.now().isoformat(),
                'verification_method': 'auto_renewal'
            })

            # تفعيل الاشتراك
            await self.activate_subscription(user_id, transaction_id)

            # إرسال إشعار بنجاح التجديد
            await self.send_subscription_notification(user_id, 'renewal_success')

            logger.info(f"تم تجديد الاشتراك تلقائياً للمستخدم {user_id}")
            return True

        except Exception as e:
            logger.error(f"خطأ في تجديد الاشتراك تلقائياً للمستخدم {user_id}: {str(e)}")

            # إرسال إشعار بفشل التجديد
            await self.send_subscription_notification(user_id, 'renewal_failed')

            return False
