import logging
from datetime import datetime
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.constants import ParseMode
from telegram.ext import CallbackContext

# Configure logging
logging.basicConfig(format='%(asctime)s - %(name)s - %(levelname)s - %(message)s', level=logging.INFO)
logger = logging.getLogger(__name__)

async def handle_payment_verification(update: Update, context: CallbackContext):
    """معالجة التحقق من الدفع"""
    # تعريف متغير lang خارج نطاق try/except للوصول إليه في حالة الخطأ
    lang = 'ar'
    
    try:
        user_id = str(update.effective_user.id)
        query = update.callback_query
        
        # استيراد المكتبات اللازمة
        from firebase_admin import firestore
        from integrations.paypal_payment import verify_paypal_transaction
        
        # الحصول على قاعدة البيانات
        db = firestore.client()
        
        # الحصول على إعدادات المستخدم
        user_settings_ref = db.collection('user_settings').document(user_id)
        user_settings_doc = user_settings_ref.get()
        
        if user_settings_doc.exists:
            settings = user_settings_doc.to_dict()
            lang = settings.get('lang', 'ar')
        else:
            lang = 'ar'
        
        # التحقق من معرف المعاملة
        transaction_id = None
        if query.data.startswith('verify_payment_'):
            transaction_id = query.data.split('_')[2]
        
        # إظهار رسالة انتظار
        await query.answer(
            "جاري التحقق من الدفع..." if lang == 'ar' else "Verifying payment...",
            show_alert=False
        )
        
        # تحديث الرسالة لإظهار حالة التحقق
        verification_message = (
            "⏳ *جاري التحقق من الدفع...*\n\n"
            "يرجى الانتظار بينما نتحقق من معاملتك.\n"
            "قد تستغرق هذه العملية بضع ثوانٍ."
        ) if lang == 'ar' else (
            "⏳ *Verifying Payment...*\n\n"
            "Please wait while we verify your transaction.\n"
            "This process may take a few seconds."
        )
        
        await query.edit_message_text(
            text=verification_message,
            parse_mode=ParseMode.MARKDOWN
        )
        
        # التحقق من الدفع
        payment_verified = await verify_paypal_transaction(user_id, 5.0, transaction_id)
        
        if payment_verified:
            # تم التحقق من الدفع بنجاح
            success_message = (
                "✅ *تم التحقق من الدفع بنجاح!*\n\n"
                "تم تفعيل اشتراكك بنجاح.\n"
                "يمكنك الآن الاستمتاع بجميع الميزات المتقدمة.\n\n"
                "• تحليلات غير محدودة\n"
                "• جميع المؤشرات الفنية\n"
                "• تنبيهات سعرية غير محدودة\n"
                "• تحليل بالذكاء الاصطناعي\n\n"
                "شكراً لاشتراكك! 🎉"
            ) if lang == 'ar' else (
                "✅ *Payment Verified Successfully!*\n\n"
                "Your subscription has been activated.\n"
                "You can now enjoy all premium features.\n\n"
                "• Unlimited analyses\n"
                "• All technical indicators\n"
                "• Unlimited price alerts\n"
                "• AI-powered analysis\n\n"
                "Thank you for subscribing! 🎉"
            )
            
            keyboard = [[InlineKeyboardButton(
                "🔙 القائمة الرئيسية" if lang == 'ar' else "🔙 Main Menu",
                callback_data='back_to_main'
            )]]
            
            await query.edit_message_text(
                text=success_message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode=ParseMode.MARKDOWN
            )
            
            logger.info(f"تم التحقق من دفع المستخدم {user_id} بنجاح")
        else:
            # فشل التحقق من الدفع
            failure_message = (
                "❌ *لم يتم التحقق من الدفع*\n\n"
                "لم نتمكن من التحقق من معاملتك في هذا الوقت.\n\n"
                "الأسباب المحتملة:\n"
                "• لم تكتمل عملية الدفع بعد\n"
                "• تم إلغاء المعاملة\n"
                "• حدث خطأ في معالجة الدفع\n\n"
                "الخيارات المتاحة:\n"
                "• انتظر بضع دقائق ثم حاول التحقق مرة أخرى\n"
                "• حاول إجراء عملية دفع جديدة\n"
                "• تواصل مع الدعم إذا كنت تعتقد أن هناك خطأ"
            ) if lang == 'ar' else (
                "❌ *Payment Verification Failed*\n\n"
                "We couldn't verify your transaction at this time.\n\n"
                "Possible reasons:\n"
                "• Payment process is not completed yet\n"
                "• Transaction was cancelled\n"
                "• Error in payment processing\n\n"
                "Available options:\n"
                "• Wait a few minutes and try verifying again\n"
                "• Try making a new payment\n"
                "• Contact support if you believe there's an error"
            )
            
            keyboard = [
                [InlineKeyboardButton(
                    "🔄 إعادة التحقق" if lang == 'ar' else "🔄 Verify Again",
                    callback_data=f'verify_payment_{transaction_id}' if transaction_id else 'verify_payment'
                )],
                [InlineKeyboardButton(
                    "💳 محاولة دفع جديدة" if lang == 'ar' else "💳 Try New Payment",
                    callback_data='payment_paypal'
                )],
                [InlineKeyboardButton(
                    "🔙 القائمة الرئيسية" if lang == 'ar' else "🔙 Main Menu",
                    callback_data='back_to_main'
                )]
            ]
            
            await query.edit_message_text(
                text=failure_message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode=ParseMode.MARKDOWN
            )
            
            logger.warning(f"فشل التحقق من دفع المستخدم {user_id}")
        
    except Exception as e:
        logger.error(f"خطأ في معالجة التحقق من الدفع: {str(e)}")
        error_message = (
            "❌ حدث خطأ أثناء التحقق من الدفع. الرجاء المحاولة مرة أخرى" if lang == 'ar' else
            "❌ An error occurred while verifying payment. Please try again"
        )
        try:
            if hasattr(update, 'callback_query') and update.callback_query:
                await update.callback_query.answer(error_message, show_alert=True)

            # إعادة توجيه المستخدم إلى القائمة الرئيسية
            from main import show_main_menu
            await show_main_menu(update, context, new_message=True)
        except Exception as inner_e:
            logger.error(f"خطأ إضافي في معالجة الخطأ: {str(inner_e)}")
