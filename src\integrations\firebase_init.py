"""
وحدة تهيئة Firebase
تستخدم لتهيئة Firebase بشكل منفصل عن بقية الكود
"""

import firebase_admin
from firebase_admin import credentials, firestore
import os
import json
import logging
import traceback

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

db = None

def initialize_firebase():
    global db
    if db:
        logger.info("ℹ️ Firebase app already initialized.")
        return db

    try:
        app = None
        try:
            app = firebase_admin.get_app()
            logger.info(f"ℹ️ Using existing Firebase app: {app.name}")
        except ValueError:
            logger.info("ℹ️ No pre-initialized Firebase app found.")

        if not app:
            google_creds_env = os.getenv('GOOGLE_APPLICATION_CREDENTIALS')
            initialized = False

            if google_creds_env:
                logger.info(f"🔄 GOOGLE_APPLICATION_CREDENTIALS is set. Content (first 100 chars): '{google_creds_env[:100]}'")
                cleaned_creds = google_creds_env.strip()
                if cleaned_creds:
                    # Attempt 1: Parse as JSON string
                    try:
                        logger.info("🔄 Attempt 1: Parsing GOOGLE_APPLICATION_CREDENTIALS as JSON string.")
                        creds_dict = json.loads(cleaned_creds)
                        cred_obj = credentials.Certificate(creds_dict)
                        firebase_admin.initialize_app(cred_obj)
                        logger.info("✅ Attempt 1: Firebase initialized using JSON from GOOGLE_APPLICATION_CREDENTIALS.")
                        initialized = True
                    except json.JSONDecodeError as jde:
                        logger.error(f"❌ Attempt 1 Failed (JSONDecodeError): {jde}. Content (first 200): '{cleaned_creds[:200]}'")
                    except Exception as e_cert:
                        logger.error(f"❌ Attempt 1 Failed (Certificate/Init Error): {e_cert}")
                        logger.debug(traceback.format_exc())
                else:
                    logger.warning("⚠️ GOOGLE_APPLICATION_CREDENTIALS was empty after stripping.")

                # Attempt 2: Use as file path (if not initialized by JSON and env var was not empty)
                if not initialized and cleaned_creds:
                    try:
                        logger.info(f"🔄 Attempt 2: Using GOOGLE_APPLICATION_CREDENTIALS ('{cleaned_creds}') as file path.")
                        firebase_admin.initialize_app() # Implicitly uses env var as path
                        logger.info("✅ Attempt 2: Firebase initialized using GOOGLE_APPLICATION_CREDENTIALS as file path.")
                        initialized = True
                    except Exception as e_path:
                        logger.error(f"❌ Attempt 2 Failed (Path Init Error): {e_path}")
                        logger.debug(traceback.format_exc())
            else:
                logger.info("ℹ️ GOOGLE_APPLICATION_CREDENTIALS environment variable is not set.")

            # Attempt 3: Fallback to local file if not initialized yet
            if not firebase_admin._apps: # Check if any app was initialized
                logger.info("🔄 Attempt 3: Using local credentials file.")
                cred_file_name = 'tradingtelegram-da632-firebase-adminsdk-fbsvc-a67cf6e086.json'
                # Try common locations for the creds file
                possible_paths = [
                    os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', '..', cred_file_name), # project_root/creds.json
                    os.path.join(os.getcwd(), cred_file_name), # cwd/creds.json
                    os.path.join(os.path.dirname(os.path.abspath(__file__)), cred_file_name) # src/integrations/creds.json
                ]
                found_path = None
                for p in possible_paths:
                    if os.path.exists(p):
                        found_path = p
                        break
                
                if found_path:
                    try:
                        logger.info(f"Found local credentials file at: {found_path}")
                        cred_obj = credentials.Certificate(found_path)
                        firebase_admin.initialize_app(cred_obj)
                        logger.info("✅ Attempt 3: Firebase initialized using local file.")
                        initialized = True # Though firebase_admin._apps would be set
                    except Exception as e_local_file:
                        logger.error(f"❌ Attempt 3 Failed (Local File Init Error): {e_local_file}")
                        logger.debug(traceback.format_exc())
                else:
                    logger.error(f"❌ Attempt 3 Failed: Local credentials file ('{cred_file_name}') not found in checked paths.")

            if not firebase_admin._apps:
                 logger.critical("❌ CRITICAL: All Firebase initialization methods failed.")
                 raise RuntimeError("Failed to initialize Firebase after all attempts.")

        db = firestore.client()
        logger.info("✅ Firestore client obtained successfully.")
        return db
    except Exception as e:
        logger.critical(f"❌ Catastrophic error in Firebase initialization: {e}")
        logger.error(traceback.format_exc())
        db = None
        return None
