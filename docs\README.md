# بوت التحليل الفني للعملات الرقمية 📊💹

بوت تلغرام متخصص لتحليل أسواق العملات الرقمية باستخدام مؤشرات فنية متقدمة والذكاء الاصطناعي. يساعد المستخدمين على اتخاذ القرارات بناءً على التحليل الفني مع إمكانية إعداد تنبيهات سعرية.

## ✨ الميزات الرئيسية

- **تحليل العملات 📈**: تحليل فني شامل للعملات الرقمية باستخدام مؤشرات متعددة.
- **التنبيهات السعرية ⏰**: إعداد تنبيهات عند وصول سعر العملة إلى مستوى معين.
- **التحليل المتقدم بالذكاء الاصطناعي 🧠**: تحليلات متقدمة باستخدام Gemini AI (للمشتركين).
- **استخدام API الخاص بالمستخدم 🔑**: إمكانية استخدام مفاتيح API الخاصة بالمستخدم لتقليل التكاليف وتحسين الأداء.
- **دعم متعدد اللغات 🌐**: دعم اللغتين العربية والإنجليزية.
- **رسوم بيانية تفاعلية 📊**: عرض رسوم بيانية تفاعلية للعملات مع المؤشرات الفنية.
- **ميزات متقدمة للمشتركين 💎**: استراتيجيات تداول، تنبؤات سعرية، وتحليل متعدد الإطارات الزمنية.

## 💰 نظام الاشتراك

### النسخة المجانية 🆓
- 3 تحليلات يومياً
- مؤشرات RSI و EMA فقط
- تحديثات كل ساعة
- تنبيه واحد نشط

### النسخة المدفوعة (5 دولار/أسبوع) 💎
- تحليلات غير محدودة
- جميع المؤشرات المتقدمة
- تحديثات فورية
- تنبيهات غير محدودة
- تحليل متقدم بالذكاء الاصطناعي
- استراتيجيات تداول آلية
- تنبؤات سعرية
- تحليل متعدد الإطارات الزمنية
- الدردشة مع ال AI 

## 🔑 استخدام API الخاص بالمستخدم

يمكن للمستخدمين استخدام مفاتيح API الخاصة بهم للحصول على تجربة أفضل:

### للمستخدمين غير المشتركين
- يمكن استخدام مفاتيح Binance API الخاصة بهم للحصول على بيانات سوق أكثر دقة وتفصيلاً.
- بدون مفاتيح API، يستخدم البوت واجهة Binance العامة التي لها قيود على البيانات وعدد الطلبات.

### للمستخدمين المشتركين
- يمكن استخدام مفاتيح Binance API الخاصة بهم للحصول على بيانات سوق أكثر دقة وتفصيلاً.
- **يجب** استخدام مفاتيح Gemini API الخاصة بهم للحصول على تحليلات متقدمة بالذكاء الاصطناعي.
- بدون مفتاح Gemini API، لن تكون الميزات المتقدمة متاحة.

## 🔒 أمان مفاتيح API

- يتم تشفير جميع مفاتيح API المخزنة في قاعدة البيانات.
- لا يتم مشاركة مفاتيح API الخاصة بك مع أي طرف ثالث.
- يمكنك حذف مفاتيح API الخاصة بك في أي وقت.
- يتم حذف رسائل API فوراً بعد تخزينها في قاعدة البيانات.


## 📊 أنواع التحليل المتاحة

1. التحليل الأساسي (Basic Analysis) 📈
   - الوصف: تحليل أساسي يعتمد على المؤشرات الفنية البسيطة مثل RSI و EMA و MACD
   - المستخدمون: متاح لجميع المستخدمين (المجانيين والمشتركين)
   - المؤشرات المتاحة:
     - مؤشر القوة النسبية (RSI)
     - المتوسط المتحرك الأسي (EMA)
     - مؤشر تقارب وتباعد المتوسط المتحرك (MACD)
     - مؤشر البولنجر باند (Bollinger Bands)

2. التحليل المتقدم (Premium Analysis) 🚀
   - الوصف: تحليل متقدم يتضمن مؤشرات فنية إضافية ومتقدمة
   - المستخدمون: متاح فقط للمشتركين الذين لم يضيفو Gemini api
   - المؤشرات المتاحة:
     - جميع مؤشرات التحليل الأساسي
     - مؤشر سحابة إيشيموكو (Ichimoku Cloud)
     - مؤشر القوة النسبية الاستوكاستيك (Stochastic RSI)
     - مؤشر الاتجاه المتوسط (ADX)
     - تحليلات إضافية للاتجاهات والدعم والمقاومة

3. تحليل الذكاء الاصطناعي (AI Analysis) 🤖
   - الوصف: تحليل متقدم باستخدام نماذج الذكاء الاصطناعي (Gemini API)
   - المستخدمون: متاح فقط للمشتركين الذين أضافو Gemini API
   - الميزات:
     - تحليل شامل للسوق والعملة
     - توقعات مستقبلية مبنية على البيانات التاريخية والمؤشرات الفنية
     - تحليل الأخبار وتأثيرها على سعر العملة
     - توصيات مخصصة بناءً على جميع المؤشرات الفنية المتاحة
     - تحليل نقاط الدعم والمقاومة بدقة أعلى

## 📄 الترخيص

هذا المشروع مرخص بموجب [MIT License](LICENSE).
